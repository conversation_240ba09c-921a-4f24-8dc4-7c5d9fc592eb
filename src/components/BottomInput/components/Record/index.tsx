import { useState, useRef, useCallback } from 'react'
import { View, Text, Image, Button, Textarea } from '@tarojs/components'
import Taro from '@tarojs/taro'
import Slice77Img from '@/assets/images/input/slice-77.png'
import Slice78Img from '@/assets/images/input/slice-78.png'
import Slice79Img from '@/assets/images/input/slice-79.png'
import Slice80Img from '@/assets/images/input/slice-80.png'
import { H5RecorderManager } from '@/utils/H5RecorderManager'

interface RecordProps {
  sendMessage: (message: string) => void
  isActive?: boolean
  onShowToolbar?: () => void
  chooseImage: (type: string[]) => void
  inputType: any
}

export default function Record({ sendMessage, chooseImage, inputType, isActive = true, onShowToolbar }: RecordProps) {
  const [isRecording, setIsRecording] = useState<boolean>(false)
  const [timeLeft, setTimeLeft] = useState<number>(60)
  const [isCancelled, setIsCancelled] = useState<boolean>(false)

  // 引用
  const startY = useRef<number>(0)
  const intervalId = useRef<NodeJS.Timeout>()

  // 录音管理器 - 根据环境选择
  const recorderManager = useRef<any>(null)

  // 初始化录音管理器
  const initRecorderManager = useCallback(() => {
    if (!recorderManager.current) {
      if (Taro.getEnv() === 'WEB') {
        // H5环境使用自定义录音管理器
        recorderManager.current = new H5RecorderManager()
      } else {
        // 小程序环境使用Taro录音管理器
        recorderManager.current = Taro.getRecorderManager()
      }
    }
    return recorderManager.current
  }, [])

  // 停止录音
  const stopRecord = useCallback(() => {
    setIsRecording(false)
    const manager = initRecorderManager()
    manager.stop()
  }, [initRecorderManager])

  // 检查麦克风权限
  const checkMicrophonePermission = useCallback(async () => {
    try {
      if (Taro.getEnv() === 'WEB') {
        // H5环境检查麦克风权限
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
          console.error('浏览器不支持录音功能')
          return false
        }
        // H5环境下直接返回true，权限检查在实际录音时进行
        return true
      } else {
        // 小程序环境检查权限
        const authSetting = await Taro.getSetting()
        return !!authSetting.authSetting['scope.record']
      }
    } catch {
      return false
    }
  }, [])

  // 开始录音
  const startRecord = useCallback(
    async (event: any) => {
      console.log('开始录音', event)

      // 检查麦克风权限
      const hasPermission = await checkMicrophonePermission()
      if (!hasPermission) return

      // 如果已经在录音，直接返回
      if (isRecording) return

      setIsRecording(true)
      setTimeLeft(60)
      startY.current = event.touches[0].pageY

      // 启动倒计时
      intervalId.current = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev - 1 > 0) {
            return prev - 1
          } else {
            if (intervalId.current) {
              clearInterval(intervalId.current)
            }
            stopRecord()
            return 0
          }
        })
      }, 1000)

      // 获取录音管理器并开始录音
      const manager = initRecorderManager()
      manager.start({
        duration: 60000,
        format: 'mp3'
      })

      // 监听录音结束事件
      manager.onStop((res: any) => {
        if (intervalId.current) {
          clearInterval(intervalId.current)
        }

        // 如果被取消，直接返回
        if (isCancelled) {
          setIsCancelled(false)
          return
        }

        // 如果录音时间小于2秒，提示用户
        if (res?.duration < 2000) {
          Taro.showToast({ title: '录音时间过短', icon: 'none' })
          return
        }

        // 这里可以处理录音文件，但按要求不包含上传逻辑
        console.log('录音完成:', res)
        Taro.showToast({ title: '录音完成', icon: 'success' })
      })
    },
    [isRecording, checkMicrophonePermission, isCancelled, stopRecord, initRecorderManager]
  )

  // 触摸移动事件
  const onTouchMove = useCallback(
    (event: any) => {
      if (!isRecording) return
      console.log('startY.current', startY.current)
      console.log('startY.current', event.touches, event.touches[0].pageY)
      const currentY: number = event.touches[0].pageY
      const distance: number = startY.current - currentY

      if (distance > 100) {
        setIsCancelled(true)
        Taro.showToast({ title: '录音已取消', icon: 'none' })
        stopRecord()
        setIsRecording(false)
      }
    },
    [isRecording, stopRecord]
  )

  // 取消录音
  const tapCancel = useCallback(() => {
    const timer = setTimeout(() => {
      if (isRecording) {
        stopRecord()
        clearTimeout(timer)
        setIsRecording(false)
      }
    }, 600)
  }, [isRecording, stopRecord])

  return (
    <>
      <div className="px-[40px] z-50 relative">
        <div className="h-[112px] rounded-[24px] bg-white shadow-[0_20px_40px_0_rgba(48,48,48,0.08)] flex items-center justify-between relative">
          <div onClick={() => chooseImage(['camera'])} className="ml-[24px] mr-[8px] flex_center">
            <Image className="w-[48px] h-[48px]" src={Slice77Img} />
          </div>
          <div className="flex-1 h-full">
            <div
              className={`w-full h-full rounded-[24px] z-10 left-0 top-0 flex_center ${isRecording ? 'absolute bg-black' : ''}`}
              onTouchStart={startRecord}
              onTouchCancel={stopRecord}
              onTouchEnd={stopRecord}
              onTouchMove={onTouchMove}
              onClick={tapCancel}
            >
              <span>{isRecording ? '松开发送语音' : '按住说话'}</span>
            </div>
          </div>
          <div onClick={() => inputType.set((v) => (v === 'text' ? 'record' : 'text'))} className="ml-[8px] flex_center">
            <Image className="w-[48px] h-[48px]" src={inputType.val === 'text' ? Slice79Img : Slice80Img} />
          </div>
          <div onClick={() => chooseImage(['album'])} className="mr-[24px] ml-[16px] flex_center">
            <Image className="w-[48px] h-[48px]" src={Slice78Img} />
          </div>
        </div>
      </div>

      {/* 录音弹窗 */}
      {/* <div className="w-full h-[288px] fixed left-0 bottom-0 z-40 color_shadow rounded-[24px_24px_0_0] flex justify-center pt-[52px] box-border">
        <div className="font-normal text-[28px] text-[#202020] leading-[40px] text-center not-italic">松开发送，上移取消</div>
      </div> */}
    </>
  )
}
