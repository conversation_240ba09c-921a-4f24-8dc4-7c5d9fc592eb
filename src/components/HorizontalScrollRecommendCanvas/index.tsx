import { Canvas } from '@tarojs/components'
import { useEffect, useRef, useState, useCallback, useMemo } from 'react'
import Taro from '@tarojs/taro'
import { DesignPromptListRes } from '@/api'

interface RecommendItem {
  title: string
  content: string
}

interface HorizontalScrollRecommendCanvasProps {
  /** 推荐列表数据 */
  list: DesignPromptListRes['data']
  /** 自动滚动速度，单位：px/s，默认30 */
  scrollSpeed?: number
  /** 是否启用自动滚动，默认true */
  autoScroll?: boolean
  /** 点击事件回调 */
  onItemClick?: (item: RecommendItem, index: number) => void
  /** 是否可以手动滑动，默认true */
  allowManualScroll?: boolean
}

// 获取动态Canvas配置
const getCanvasConfig = () => {
  const systemInfo = Taro.getSystemInfoSync()
  const screenWidth = systemInfo.screenWidth || 375

  console.log('获取Canvas配置:', {
    屏幕宽度: screenWidth,
    系统信息: systemInfo
  })

  return {
    width: screenWidth, // 动态获取屏幕宽度
    height: 74, // 实际显示高度（148的一半）
    itemHeight: 32, // 单个item高度（64的一半）
    itemGap: 8, // item间距（16的一半）
    rowGap: 10, // 行间距（20的一半）
    paddingX: 20, // 左右内边距（40的一半）
    fontSize: 12, // 字体大小（24的一半）
    borderRadius: 6, // 圆角（12的一半）
    backgroundColor: '#ffffff', // item背景色
    textColor: '#535353', // 文字颜色
    itemPaddingX: 10 // item内边距（20的一半）
  }
}

// 性能优化：预计算item宽度
const calculateItemWidth = (text: string, fontSize: number, itemPaddingX: number): number => {
  // 简单的文字宽度估算，中文字符按fontSize*1.2计算，英文按fontSize*0.8计算
  let width = 0
  for (let i = 0; i < text.length; i++) {
    const char = text.charAt(i)
    if (/[\u4e00-\u9fa5]/.test(char)) {
      width += fontSize * 1.2 // 中文字符稍宽一些
    } else {
      width += fontSize * 0.8 // 英文字符
    }
  }
  return width + itemPaddingX * 2
}

const HorizontalScrollRecommendCanvas: React.FC<HorizontalScrollRecommendCanvasProps> = ({
  list,
  scrollSpeed = 30,
  autoScroll = true,
  onItemClick,
  allowManualScroll = true
}) => {
  const contextRef = useRef<any>(null)
  const animationRef = useRef<number | null>(null)
  const [canvasId] = useState(`canvas-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`)

  // 动态获取Canvas配置 - 使用useMemo缓存
  const CANVAS_CONFIG = useMemo(() => getCanvasConfig(), [])

  // 获取设备像素比用于Canvas尺寸设置
  const getCanvasSize = useCallback(() => {
    const systemInfo = Taro.getSystemInfoSync()
    const dpr = systemInfo.pixelRatio || 2
    return {
      displayWidth: CANVAS_CONFIG.width,
      displayHeight: CANVAS_CONFIG.height,
      canvasWidth: CANVAS_CONFIG.width * dpr,
      canvasHeight: CANVAS_CONFIG.height * dpr,
      dpr
    }
  }, [CANVAS_CONFIG.width, CANVAS_CONFIG.height])

  // 滚动状态
  const translateXRef = useRef(0)
  const [isPaused, setIsPaused] = useState(false)

  // 手动滚动状态
  const [isDragging, setIsDragging] = useState(false)
  const [startX, setStartX] = useState(0)
  const [startTranslateX, setStartTranslateX] = useState(0)
  const pauseTimerRef = useRef<NodeJS.Timeout | null>(null)
  const [hasMoved, setHasMoved] = useState(false) // 用于区分点击和拖拽

  // 预计算的数据
  const [renderData, setRenderData] = useState<{
    row1: Array<{ item: RecommendItem; width: number; x: number }>
    row2: Array<{ item: RecommendItem; width: number; x: number }>
    totalWidth: number
  }>({ row1: [], row2: [], totalWidth: 0 })

  // 准备渲染数据 - 预计算位置和宽度
  const prepareRenderData = useCallback(() => {
    if (list.length === 0) return { row1: [], row2: [], totalWidth: 0 }

    const row1: Array<{ item: RecommendItem; width: number; x: number }> = []
    const row2: Array<{ item: RecommendItem; width: number; x: number }> = []

    let row1X = 0
    let row2X = 0

    list.forEach((item, index) => {
      const width = calculateItemWidth(item.title, CANVAS_CONFIG.fontSize, CANVAS_CONFIG.itemPaddingX)

      if (index % 2 === 0) {
        row1.push({ item, width, x: row1X })
        row1X += width + CANVAS_CONFIG.itemGap
      } else {
        row2.push({ item, width, x: row2X })
        row2X += width + CANVAS_CONFIG.itemGap
      }
    })

    const totalWidth = Math.max(row1X, row2X)

    console.log('数据分配结果:', {
      总数据: list.length,
      第一行: row1.length,
      第二行: row2.length,
      第一行宽度: row1X,
      第二行宽度: row2X,
      总宽度: totalWidth,
      第一行数据: row1.map((item) => item.item.title),
      第二行数据: row2.map((item) => item.item.title)
    })

    return { row1, row2, totalWidth }
  }, [list, CANVAS_CONFIG.fontSize, CANVAS_CONFIG.itemGap, CANVAS_CONFIG.itemPaddingX])

  // 更新渲染数据
  useEffect(() => {
    const data = prepareRenderData()
    setRenderData(data)

    // 重置滚动位置
    const initialPosition = -data.totalWidth
    translateXRef.current = initialPosition
  }, [prepareRenderData])

  // 旧版Canvas绘制方法
  const drawLegacyCanvas = useCallback(() => {
    const ctx = contextRef.current
    if (!ctx || renderData.totalWidth === 0) {
      // console.log('旧版Canvas绘制跳过:', { ctx: !!ctx, totalWidth: renderData.totalWidth })
      return
    }

    // 检查Canvas是否已经准备好
    if (!canvasId) {
      console.log('Canvas ID未准备好，跳过绘制')
      return
    }

    // 清空画布
    try {
      if (typeof ctx.clearRect === 'function') {
        ctx.clearRect(0, 0, CANVAS_CONFIG.width, CANVAS_CONFIG.height)
      }
    } catch (error) {
      console.warn('Canvas清空失败:', error)
    }

    // 设置字体 - 简化处理，避免参数问题
    try {
      // 使用数字而不是字符串
      const fontSize = Number(CANVAS_CONFIG.fontSize) || 24
      if (typeof ctx.setFontSize === 'function') {
        ctx.setFontSize(fontSize)
      }
      if (typeof ctx.setTextAlign === 'function') {
        ctx.setTextAlign('left')
      }
      if (typeof ctx.setTextBaseline === 'function') {
        ctx.setTextBaseline('middle')
      }
    } catch (error) {
      console.warn('Canvas字体设置失败:', error)
      // 忽略字体设置错误，继续绘制
    }

    const drawRow = (rowData: typeof renderData.row1, y: number) => {
      if (rowData.length === 0) return

      // 计算需要绘制的重复次数，确保无限滚动
      const repeatCount = Math.ceil(CANVAS_CONFIG.width / renderData.totalWidth) + 3

      for (let repeat = 0; repeat < repeatCount; repeat++) {
        const offsetX = translateXRef.current + repeat * renderData.totalWidth + CANVAS_CONFIG.paddingX

        rowData.forEach(({ item, width, x }) => {
          const itemX = offsetX + x

          // 只绘制可见区域内的item，性能优化
          if (itemX + width > -50 && itemX < CANVAS_CONFIG.width + 50) {
            try {
              // 绘制背景（带圆角）
              if (typeof ctx.setFillStyle === 'function') {
                ctx.setFillStyle(CANVAS_CONFIG.backgroundColor)
              }

              // 绘制圆角矩形背景
              if (typeof ctx.beginPath === 'function' && typeof ctx.arc === 'function') {
                const radius = CANVAS_CONFIG.borderRadius
                ctx.beginPath()
                ctx.moveTo(itemX + radius, y)
                ctx.lineTo(itemX + width - radius, y)
                ctx.arc(itemX + width - radius, y + radius, radius, -Math.PI / 2, 0)
                ctx.lineTo(itemX + width, y + CANVAS_CONFIG.itemHeight - radius)
                ctx.arc(itemX + width - radius, y + CANVAS_CONFIG.itemHeight - radius, radius, 0, Math.PI / 2)
                ctx.lineTo(itemX + radius, y + CANVAS_CONFIG.itemHeight)
                ctx.arc(itemX + radius, y + CANVAS_CONFIG.itemHeight - radius, radius, Math.PI / 2, Math.PI)
                ctx.lineTo(itemX, y + radius)
                ctx.arc(itemX + radius, y + radius, radius, Math.PI, -Math.PI / 2)
                ctx.closePath()
                if (typeof ctx.fill === 'function') {
                  ctx.fill()
                }
              } else {
                // 降级到普通矩形
                if (typeof ctx.fillRect === 'function') {
                  ctx.fillRect(itemX, y, width, CANVAS_CONFIG.itemHeight)
                }
              }

              // 绘制文字
              if (typeof ctx.setFillStyle === 'function') {
                ctx.setFillStyle(CANVAS_CONFIG.textColor)
              }
              const textX = itemX + CANVAS_CONFIG.itemPaddingX
              const textY = y + CANVAS_CONFIG.itemHeight / 2
              if (typeof ctx.fillText === 'function') {
                ctx.fillText(item.title, textX, textY)
              }
            } catch (error) {
              console.warn('Canvas绘制项目失败:', error, item.title)
            }
          }
        })
      }
    }

    // 绘制两行
    // console.log('开始绘制两行:', {
    //   第一行数量: renderData.row1.length,
    //   第二行数量: renderData.row2.length,
    //   第一行Y位置: 0,
    //   第二行Y位置: CANVAS_CONFIG.itemHeight + CANVAS_CONFIG.rowGap
    // })

    drawRow(renderData.row1, 0)
    drawRow(renderData.row2, CANVAS_CONFIG.itemHeight + CANVAS_CONFIG.rowGap)

    // 旧版API需要调用draw方法
    try {
      if (typeof ctx.draw === 'function') {
        ctx.draw()
        // console.log('Canvas draw调用成功')
      }
    } catch (error) {
      console.warn('Canvas draw失败:', error)
    }

    // console.log('旧版Canvas绘制完成:', {
    //   translateX: translateXRef.current,
    //   row1Count: renderData.row1.length,
    //   row2Count: renderData.row2.length
    // })
  }, [renderData, canvasId, CANVAS_CONFIG])

  // 初始化Canvas
  const initCanvas = useCallback(async () => {
    try {
      console.log('开始初始化Canvas, 环境:', process.env.TARO_ENV)

      // 获取设备像素比
      const systemInfo = Taro.getSystemInfoSync()
      const dpr = systemInfo.pixelRatio || 2
      console.log('设备像素比:', dpr)

      // 直接使用旧版Canvas API，更稳定
      console.log('使用旧版Canvas API初始化, canvasId:', canvasId)
      const ctx = Taro.createCanvasContext(canvasId)
      console.log('Canvas上下文创建结果:', ctx, '类型:', typeof ctx)

      if (ctx) {
        // 检查上下文方法
        console.log('Canvas方法检查:', {
          clearRect: typeof ctx.clearRect,
          setFontSize: typeof ctx.setFontSize,
          setFillStyle: typeof ctx.setFillStyle,
          fillRect: typeof ctx.fillRect,
          fillText: typeof ctx.fillText,
          draw: typeof ctx.draw,
          scale: typeof ctx.scale
        })

        // 设置Canvas缩放以适应高DPI屏幕
        if (typeof ctx.scale === 'function' && dpr > 1) {
          try {
            ctx.scale(dpr, dpr)
            console.log('Canvas缩放设置成功, DPR:', dpr)
          } catch (scaleError) {
            console.warn('Canvas缩放设置失败:', scaleError)
          }
        }

        contextRef.current = ctx
        console.log('旧版Canvas初始化成功')

        // 延迟绘制，确保Canvas准备就绪
        setTimeout(() => {
          // 再次检查上下文是否有效
          if (contextRef.current) {
            drawLegacyCanvas()
          }
        }, 200)
      } else {
        console.error('旧版Canvas初始化失败')
      }
    } catch (error) {
      console.error('Canvas初始化失败:', error)
    }
  }, [canvasId, drawLegacyCanvas])

  // 自动滚动动画
  useEffect(() => {
    if (!autoScroll || isPaused || isDragging || renderData.totalWidth === 0) {
      return
    }

    const animate = () => {
      // 计算移动距离
      const moveDistance = (scrollSpeed * 16.67) / 1000 // 60fps
      translateXRef.current -= moveDistance

      // 无限循环逻辑
      if (translateXRef.current <= -renderData.totalWidth * 2) {
        translateXRef.current += renderData.totalWidth
      } else if (translateXRef.current >= 0) {
        translateXRef.current -= renderData.totalWidth
      }

      // 使用旧版Canvas API绘制
      drawLegacyCanvas()

      animationRef.current = requestAnimationFrame(animate)
    }

    animationRef.current = requestAnimationFrame(animate)

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
        animationRef.current = null
      }
    }
  }, [autoScroll, isPaused, isDragging, scrollSpeed, renderData.totalWidth, drawLegacyCanvas])

  // 暂停和恢复滚动
  const pauseAutoScroll = useCallback(() => {
    setIsPaused(true)
    if (pauseTimerRef.current) {
      clearTimeout(pauseTimerRef.current)
    }
  }, [])

  const resumeAutoScrollDelayed = useCallback(() => {
    if (pauseTimerRef.current) {
      clearTimeout(pauseTimerRef.current)
    }
    pauseTimerRef.current = setTimeout(() => {
      setIsPaused(false)
    }, 1000)
  }, [])

  // 触摸事件处理
  const handleTouchStart = useCallback(
    (e: any) => {
      if (!allowManualScroll) return

      setIsDragging(true)
      setHasMoved(false)
      pauseAutoScroll()

      // 兼容小程序和H5的触摸坐标获取
      const touch = e.touches[0]
      const touchX = touch.clientX || touch.x || 0
      setStartX(touchX)
      setStartTranslateX(translateXRef.current)
    },
    [allowManualScroll, pauseAutoScroll]
  )

  const handleTouchMove = useCallback(
    (e: any) => {
      if (!isDragging || !allowManualScroll) return

      // 兼容小程序和H5的触摸坐标获取
      const touch = e.touches[0]
      const touchX = touch.clientX || touch.x || 0
      const deltaX = touchX - startX

      // 如果移动距离超过阈值，标记为已移动
      if (Math.abs(deltaX) > 5) {
        setHasMoved(true)
      }

      let newTranslateX = startTranslateX + deltaX

      // 无限滚动边界处理
      if (newTranslateX <= -renderData.totalWidth * 2) {
        newTranslateX += renderData.totalWidth
      } else if (newTranslateX >= 0) {
        newTranslateX -= renderData.totalWidth
      }

      translateXRef.current = newTranslateX
      // 使用旧版Canvas API绘制
      drawLegacyCanvas()
    },
    [isDragging, allowManualScroll, startX, startTranslateX, renderData.totalWidth, drawLegacyCanvas]
  )

  const handleTouchEnd = useCallback(() => {
    if (!isDragging) return

    setIsDragging(false)
    resumeAutoScrollDelayed()

    // 延迟重置hasMoved，避免影响点击事件
    setTimeout(() => {
      setHasMoved(false)
    }, 100)
  }, [isDragging, resumeAutoScrollDelayed])

  // Canvas点击事件处理
  const handleCanvasTap = useCallback(
    (e: any) => {
      console.log('Canvas点击事件触发:', {
        事件对象: e,
        事件类型: e.type,
        detail: e.detail,
        touches: e.touches,
        currentTarget: e.currentTarget
      })

      // 如果刚刚进行了拖拽，不触发点击事件
      if (!onItemClick || renderData.totalWidth === 0 || hasMoved) {
        console.log('点击事件跳过:', { onItemClick: !!onItemClick, totalWidth: renderData.totalWidth, hasMoved })
        return
      }

      // 获取点击坐标，兼容小程序和H5
      let canvasX: number, canvasY: number
      const canvasSize = getCanvasSize()

      // 小程序环境的坐标获取
      if (Taro.getEnv() === 'WEAPP') {
        // 小程序Canvas点击事件的坐标获取
        if (e.detail && (e.detail.x !== undefined || e.detail.y !== undefined)) {
          canvasX = e.detail.x || 0
          canvasY = e.detail.y || 0
        } else if (e.touches && e.touches[0]) {
          canvasX = e.touches[0].x || 0
          canvasY = e.touches[0].y || 0
        } else if (e.changedTouches && e.changedTouches[0]) {
          canvasX = e.changedTouches[0].x || 0
          canvasY = e.changedTouches[0].y || 0
        } else {
          console.log('小程序无法获取点击坐标，事件对象:', {
            type: e.type,
            detail: e.detail,
            touches: e.touches,
            changedTouches: e.changedTouches,
            完整事件: e
          })
          return
        }
        console.log('小程序点击坐标:', {
          x: canvasX,
          y: canvasY,
          事件类型: e.type,
          事件详情: e.detail
        })
      } else if (e.touches && e.touches[0]) {
        // H5环境 - 触摸事件
        const touch = e.touches[0]
        const rect = e.currentTarget.getBoundingClientRect()
        canvasX = (touch.clientX - rect.left) * canvasSize.dpr // 使用实际DPR
        canvasY = (touch.clientY - rect.top) * canvasSize.dpr
        console.log('H5触摸坐标:', {
          原始: { x: touch.clientX - rect.left, y: touch.clientY - rect.top },
          缩放后: { x: canvasX, y: canvasY },
          DPR: canvasSize.dpr
        })
      } else {
        // H5环境 - 鼠标事件
        const rect = e.currentTarget.getBoundingClientRect()
        canvasX = (e.clientX - rect.left) * canvasSize.dpr // 使用实际DPR
        canvasY = (e.clientY - rect.top) * canvasSize.dpr
        console.log('H5鼠标坐标:', {
          原始: { x: e.clientX - rect.left, y: e.clientY - rect.top },
          缩放后: { x: canvasX, y: canvasY },
          DPR: canvasSize.dpr
        })
      }

      console.log('点击坐标:', { canvasX, canvasY })
      console.log('Canvas配置:', {
        itemHeight: CANVAS_CONFIG.itemHeight,
        rowGap: CANVAS_CONFIG.rowGap,
        paddingX: CANVAS_CONFIG.paddingX,
        width: CANVAS_CONFIG.width
      })
      console.log('渲染数据:', {
        row1数量: renderData.row1.length,
        row2数量: renderData.row2.length,
        totalWidth: renderData.totalWidth,
        当前translateX: translateXRef.current
      })

      // 判断点击的是哪一行
      let rowData: typeof renderData.row1
      let rowName: string

      if (canvasY <= CANVAS_CONFIG.itemHeight) {
        rowData = renderData.row1
        rowName = '第一行'
      } else if (
        canvasY >= CANVAS_CONFIG.itemHeight + CANVAS_CONFIG.rowGap &&
        canvasY <= CANVAS_CONFIG.itemHeight * 2 + CANVAS_CONFIG.rowGap
      ) {
        rowData = renderData.row2
        rowName = '第二行'
      } else {
        console.log('点击在空白区域:', {
          canvasY,
          第一行范围: `0 - ${CANVAS_CONFIG.itemHeight}`,
          第二行范围: `${CANVAS_CONFIG.itemHeight + CANVAS_CONFIG.rowGap} - ${CANVAS_CONFIG.itemHeight * 2 + CANVAS_CONFIG.rowGap}`
        })
        return // 点击在空白区域
      }

      console.log(`点击在${rowName}，该行有${rowData.length}个item`)

      // 计算点击的item - 使用与绘制相同的坐标计算方式
      const repeatCount = Math.ceil(CANVAS_CONFIG.width / renderData.totalWidth) + 3

      console.log('X坐标命中检测开始:', {
        repeatCount,
        translateX: translateXRef.current,
        paddingX: CANVAS_CONFIG.paddingX,
        totalWidth: renderData.totalWidth
      })

      for (let repeat = 0; repeat < repeatCount; repeat++) {
        // 使用与绘制相同的offsetX计算方式
        const offsetX = translateXRef.current + repeat * renderData.totalWidth + CANVAS_CONFIG.paddingX
        console.log(`检测重复${repeat}，offsetX: ${offsetX}`)

        for (let i = 0; i < rowData.length; i++) {
          const { item, width, x: itemOffsetX } = rowData[i]
          const itemX = offsetX + itemOffsetX // 与绘制逻辑保持一致
          const itemRight = itemX + width

          console.log(`检测item ${i}:`, {
            title: item.title,
            itemX,
            itemRight,
            width,
            itemOffsetX,
            点击X: canvasX,
            是否命中: canvasX >= itemX && canvasX <= itemRight
          })

          if (canvasX >= itemX && canvasX <= itemRight) {
            // 计算正确的索引
            const actualIndex = i % list.length
            console.log('🎯 点击命中:', {
              点击的item: item,
              索引: actualIndex,
              原始数据: list[actualIndex],
              坐标: { canvasX, canvasY },
              item区域: { x: itemX, width, right: itemRight },
              行: rowName
            })
            onItemClick(item, actualIndex)
            return
          }
        }
      }

      console.log('❌ 没有命中任何item，总结:', {
        点击坐标: { canvasX, canvasY },
        Canvas尺寸: { width: CANVAS_CONFIG.width, height: CANVAS_CONFIG.height },
        行判断: rowName,
        检测的行数据: rowData.length,
        translateX: translateXRef.current,
        重复次数: repeatCount
      })
    },
    [
      onItemClick,
      renderData,
      list,
      hasMoved,
      CANVAS_CONFIG.itemHeight,
      CANVAS_CONFIG.paddingX,
      CANVAS_CONFIG.rowGap,
      CANVAS_CONFIG.width,
      CANVAS_CONFIG.height,
      getCanvasSize
    ]
  )

  // 初始化Canvas
  useEffect(() => {
    if (renderData.totalWidth > 0) {
      // 延迟初始化，确保Canvas元素完全渲染
      const timer = setTimeout(() => {
        initCanvas()
      }, 500)

      return () => clearTimeout(timer)
    }
  }, [renderData.totalWidth, initCanvas])

  // 组件卸载清理
  useEffect(() => {
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
      if (pauseTimerRef.current) {
        clearTimeout(pauseTimerRef.current)
      }
    }
  }, [])

  if (list.length === 0) {
    return null
  }

  const canvasSize = getCanvasSize()

  console.log('Canvas尺寸配置:', canvasSize)

  return (
    <div className="w-full mt-[48px]">
      <Canvas
        canvasId={canvasId}
        width={`${canvasSize.canvasWidth}`}
        height={`${canvasSize.canvasHeight}`}
        style={{
          width: `${canvasSize.displayWidth}px`,
          height: `${canvasSize.displayHeight}px`
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onTap={handleCanvasTap}
        onClick={handleCanvasTap}
        onLongTap={handleCanvasTap}
      />
    </div>
  )
}

export default HorizontalScrollRecommendCanvas
