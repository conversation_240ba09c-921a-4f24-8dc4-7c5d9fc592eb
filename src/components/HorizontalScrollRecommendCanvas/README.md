# HorizontalScrollRecommendCanvas 组件

基于原生Canvas实现的高性能水平滚动推荐组件，专为小程序和H5环境优化。

## 特性

- ✅ 原生Canvas实现，性能优异
- ✅ 小程序和H5双端兼容
- ✅ 自动无限滚动
- ✅ 支持手动滑动
- ✅ 点击事件支持
- ✅ 内存优化，避免DOM节点过多
- ✅ 可配置滚动速度
- ✅ 响应式设计

## 性能优势

相比DOM版本的HorizontalScrollRecommend组件：

1. **内存占用更低**: 使用Canvas绘制，避免创建大量DOM节点
2. **渲染性能更好**: 原生Canvas绘制，减少浏览器重排重绘
3. **小程序兼容性更强**: 避免小程序DOM操作限制
4. **包体积更小**: 不依赖第三方滚动库

## 使用方法

```tsx
import HorizontalScrollRecommendCanvas from '@/components/HorizontalScrollRecommendCanvas'

const MyComponent = () => {
  const [recommendList, setRecommendList] = useState([
    { title: '推荐内容1', content: '描述1' },
    { title: '推荐内容2', content: '描述2' },
    // ...更多数据
  ])

  const handleItemClick = (item, index) => {
    console.log('点击了:', item, index)
  }

  return (
    <HorizontalScrollRecommendCanvas
      list={recommendList}
      scrollSpeed={30}
      autoScroll={true}
      allowManualScroll={true}
      onItemClick={handleItemClick}
    />
  )
}
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| list | DesignPromptListRes['data'] | - | 推荐列表数据 |
| scrollSpeed | number | 30 | 自动滚动速度，单位：px/s |
| autoScroll | boolean | true | 是否启用自动滚动 |
| allowManualScroll | boolean | true | 是否可以手动滑动 |
| onItemClick | (item, index) => void | - | 点击事件回调 |

## 配置说明

组件内置了Canvas配置常量，可根据需要调整：

```tsx
const CANVAS_CONFIG = {
  width: 750,           // 设计稿宽度
  height: 148,          // 组件高度
  itemHeight: 64,       // 单个item高度
  itemGap: 16,          // item间距
  rowGap: 20,           // 行间距
  paddingX: 40,         // 左右内边距
  fontSize: 24,         // 字体大小
  borderRadius: 12,     // 圆角
  backgroundColor: '#ffffff', // item背景色
  textColor: '#535353', // 文字颜色
  itemPaddingX: 20      // item内边距
}
```

## 注意事项

1. 组件会自动将数据分为两行显示
2. 使用Canvas绘制，确保在小程序中正确配置Canvas权限
3. 点击事件通过Canvas的tap事件实现，精确度较高
4. 组件会自动处理无限滚动的边界情况
5. 手动滑动时会暂停自动滚动，1秒后恢复

## 问题解决

### 点击事件不触发
- ✅ 兼容小程序和H5的坐标获取方式
- ✅ 正确处理Canvas缩放比例
- ✅ 区分拖拽和点击操作
- ✅ 添加调试日志便于排查

### 手动滚动内容消失
- ✅ 修复触摸坐标获取兼容性问题
- ✅ 优化Canvas重绘时机
- ✅ 添加roundRect兼容性处理
- ✅ 完善错误处理和日志

### canvas.getContext is not a function
- ✅ 统一使用旧版Canvas API (Taro.createCanvasContext)
- ✅ 避免Canvas 2D API兼容性问题
- ✅ 确保小程序环境下的稳定性
- ✅ 简化Canvas初始化流程

### Canvas方法兼容性问题
- ✅ 为所有Canvas方法添加类型检查和错误处理
- ✅ 兼容处理 setFontSize, setFillStyle, fillRect, fillText 等方法
- ✅ 添加详细的调试日志便于问题排查
- ✅ 延迟绘制确保Canvas准备就绪

### 显示样式问题
- ✅ 修复Canvas尺寸配置，确保两行内容正确显示
- ✅ 添加圆角矩形绘制，匹配原始组件样式
- ✅ 调整字体大小和间距，适配实际显示需求
- ✅ 优化文字宽度计算，提高布局准确性

### Canvas显示模糊问题
- ✅ 添加设备像素比（DPR）支持，解决高分辨率屏幕模糊问题
- ✅ 设置Canvas实际像素尺寸为显示尺寸的DPR倍数
- ✅ 在Canvas初始化时应用scale变换适配高DPI屏幕
- ✅ 保持绘制坐标不变，由Canvas自动处理缩放

### 动态尺寸适配
- ✅ Canvas宽度动态获取屏幕宽度，适配不同设备
- ✅ 使用useMemo缓存Canvas配置，避免重复计算
- ✅ 所有相关函数的依赖数组正确更新，确保响应式更新
- ✅ 支持横屏、竖屏等不同屏幕方向的自适应

## 调试

组件内置了详细的调试日志，可以在控制台查看：
- Canvas初始化状态和设备像素比
- 绘制完成信息和数据分配
- 点击事件触发情况和坐标计算
- Canvas尺寸配置和DPR设置
- 点击命中检测和item数据结构

### 点击事件调试
当点击事件无法正常触发时，检查控制台日志：
1. `Canvas点击事件触发` - 确认事件是否被触发
2. `点击坐标` - 确认坐标计算是否正确
3. `点击命中` - 确认是否命中了具体的item
4. 验证传递的item对象是否包含完整的title和content字段

## 兼容性

- ✅ 微信小程序
- ✅ H5
- ✅ 其他Taro支持的平台

## 测试

可以使用 `demo.tsx` 文件进行功能测试：

```tsx
import Demo from '@/components/HorizontalScrollRecommendCanvas/demo'

// 在页面中使用
<Demo />
```
