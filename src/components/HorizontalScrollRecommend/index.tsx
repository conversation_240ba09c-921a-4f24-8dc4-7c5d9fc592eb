import { Image } from '@tarojs/components'
import { useEffect, useRef, useState, useCallback } from 'react'
import { DesignPromptListRes } from '@/api'
import { createFixedIntervalAnimation, calculateMoveDistance } from '../../utils/animation'

interface RecommendItem {
  title: string
  content: string
}

interface HorizontalScrollRecommendProps {
  /** 推荐列表数据 */
  list: DesignPromptListRes['data']
  /** 自动滚动速度，单位：px/s，默认30 */
  scrollSpeed?: number
  /** 是否启用自动滚动，默认true */
  autoScroll?: boolean
  /** 点击事件回调 */
  onItemClick?: (item: RecommendItem, index: number) => void
  /** 是否可以手动滑动，默认true */
  allowManualScroll?: boolean
}

const HorizontalScrollRecommend: React.FC<HorizontalScrollRecommendProps> = ({
  list,
  scrollSpeed = 30,
  autoScroll = true,
  onItemClick,
  allowManualScroll = true
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  const animationRef = useRef<number | (() => void) | null>(null)
  const [isPaused, setIsPaused] = useState(false)
  const [translateX, setTranslateX] = useState(0)
  const translateXRef = useRef(0)

  // 手动滚动相关状态
  const [isDragging, setIsDragging] = useState(false)
  const [startX, setStartX] = useState(0)
  const [startTranslateX, setStartTranslateX] = useState(0)
  const pauseTimerRef = useRef<NodeJS.Timeout | null>(null)

  // 准备渲染数据 - 分为两行
  const prepareData = useCallback(() => {
    if (list.length === 0) return { row1: [], row2: [] }

    const row1: RecommendItem[] = []
    const row2: RecommendItem[] = []

    list.forEach((item, index) => {
      if (index % 2 === 0) {
        row1.push(item)
      } else {
        row2.push(item)
      }
    })

    return { row1, row2 }
  }, [list])

  const { row1, row2 } = prepareData()

  // 初始化动画 - 从中间位置开始，确保向前向后都有内容
  useEffect(() => {
    // 从中间位置开始，这样向前向后都有足够的内容
    const estimatedItemWidth = 180
    const itemsPerRow = Math.ceil(list.length / 2)
    const singleCycleWidth = itemsPerRow * estimatedItemWidth
    const initialPosition = -singleCycleWidth // 从第1个循环的负位置开始

    translateXRef.current = initialPosition
    setTranslateX(initialPosition)
  }, [list])

  // 计算单个循环的宽度
  const getSingleCycleWidth = useCallback(() => {
    const estimatedItemWidth = 180
    const itemsPerRow = Math.ceil(list.length / 2)
    return itemsPerRow * estimatedItemWidth
  }, [list.length])

  // 核心动画逻辑 - 小程序环境优化的无限滚动
  useEffect(() => {
    if (!autoScroll || isPaused || isDragging || list.length === 0) {
      return
    }

    const singleCycleWidth = getSingleCycleWidth()

    // 使用小程序兼容的动画函数
    const stopAnimation = createFixedIntervalAnimation(() => {
      // 使用固定的移动距离，确保在小程序中稳定运行
      const moveDistance = calculateMoveDistance(scrollSpeed, 60)
      translateXRef.current -= moveDistance

      // 智能的位置重置：当滚动超过边界时，平滑重置到等效位置
      // 确保向前向后都能无限滚动
      if (translateXRef.current <= -singleCycleWidth * 2) {
        translateXRef.current = translateXRef.current + singleCycleWidth
      } else if (translateXRef.current >= 0) {
        translateXRef.current = translateXRef.current - singleCycleWidth
      }

      // 更新状态触发重新渲染
      setTranslateX(translateXRef.current)
    }, 16.67) // 60fps

    // 保存停止函数的引用
    animationRef.current = stopAnimation as any

    return () => {
      if (animationRef.current) {
        // 调用停止函数
        if (typeof animationRef.current === 'function') {
          animationRef.current()
        } else {
          // 兼容原来的 cancelAnimationFrame
          cancelAnimationFrame(animationRef.current)
        }
        animationRef.current = null
      }
    }
  }, [autoScroll, isPaused, isDragging, scrollSpeed, list.length, getSingleCycleWidth])

  // 暂停自动滚动的辅助函数
  const pauseAutoScroll = useCallback(() => {
    setIsPaused(true)
    if (pauseTimerRef.current) {
      clearTimeout(pauseTimerRef.current)
    }
  }, [])

  const resumeAutoScrollDelayed = useCallback(() => {
    if (pauseTimerRef.current) {
      clearTimeout(pauseTimerRef.current)
    }
    pauseTimerRef.current = setTimeout(() => {
      setIsPaused(false)
    }, 1000) // 1秒后恢复
  }, [])

  // 处理触摸事件
  const handleTouchStart = useCallback(
    (e: React.TouchEvent) => {
      if (!allowManualScroll) return

      setIsDragging(true)
      pauseAutoScroll()
      const touch = e.touches[0]
      setStartX(touch.clientX)
      setStartTranslateX(translateXRef.current)
    },
    [allowManualScroll, pauseAutoScroll]
  )

  const handleTouchMove = useCallback(
    (e: React.TouchEvent) => {
      if (!isDragging || !allowManualScroll) return

      const touch = e.touches[0]
      const deltaX = touch.clientX - startX
      let newTranslateX = startTranslateX + deltaX

      // 智能的位置管理，确保向前向后都能无限滑动
      const singleCycleWidth = getSingleCycleWidth()
      if (newTranslateX <= -singleCycleWidth * 2) {
        newTranslateX = newTranslateX + singleCycleWidth
      } else if (newTranslateX >= 0) {
        newTranslateX = newTranslateX - singleCycleWidth
      }

      translateXRef.current = newTranslateX
      setTranslateX(newTranslateX)
    },
    [isDragging, allowManualScroll, startX, startTranslateX, getSingleCycleWidth]
  )

  const handleTouchEnd = useCallback(() => {
    if (!isDragging) return

    setIsDragging(false)
    resumeAutoScrollDelayed()
  }, [isDragging, resumeAutoScrollDelayed])

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (animationRef.current) {
        // 处理不同类型的动画引用
        if (typeof animationRef.current === 'function') {
          animationRef.current()
        } else {
          cancelAnimationFrame(animationRef.current)
        }
      }
      if (pauseTimerRef.current) {
        clearTimeout(pauseTimerRef.current)
      }
    }
  }, [])

  // 渲染单行内容（创建大量重复内容实现真正的无限滚动）
  const renderRow = useCallback(
    (rowData: RecommendItem[], rowIndex: number) => {
      if (rowData.length === 0) return null

      // 创建足够的重复内容，确保向前向后都有内容显示
      const repeatedContent: JSX.Element[] = []
      const repeatCount = 9 // 9次重复，确保向前向后都有足够内容

      for (let i = 0; i < repeatCount; i++) {
        repeatedContent.push(
          <div key={`repeat-${i}`} className="flex gap-[16px] h-[64px] items-center mr-[16px]">
            {rowData.map((item, index) => (
              <div
                key={`${rowIndex}-${i}-${index}`}
                className="flex items-center justify-center h-[64px] bg-white rounded-[12px] px-[20px] whitespace-nowrap flex-shrink-0 min-w-auto cursor-pointer transition-transform duration-200 ease-in-out active:scale-95"
                onClick={() => onItemClick?.(item, index % list.length)}
              >
                <div className="font-normal text-[24px] text-[#535353] leading-[32px] text-left">{item.title}</div>
              </div>
            ))}
          </div>
        )
      }

      return <div className="flex">{repeatedContent}</div>
    },
    [list, onItemClick]
  )

  if (list.length === 0) {
    return null
  }

  return (
    <div
      className="w-full mt-[48px] overflow-hidden cursor-grab"
      ref={containerRef}
      // onMouseDown={handleMouseDown}
      // onMouseMove={handleMouseMove}
      // onMouseUp={handleMouseUp}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      style={{
        cursor: isDragging ? 'grabbing' : 'grab',
        userSelect: 'none'
      }}
    >
      <div className="h-[148px]">
        <div
          ref={contentRef}
          className="flex flex-col gap-[20px] px-[40px] w-max"
          style={{
            transform: `translateX(${translateX}px)`,
            willChange: 'transform',
            pointerEvents: isDragging ? 'none' : 'auto'
          }}
        >
          {renderRow(row1, 0)}
          {renderRow(row2, 1)}
        </div>
      </div>
    </div>
  )
}

export default HorizontalScrollRecommend
