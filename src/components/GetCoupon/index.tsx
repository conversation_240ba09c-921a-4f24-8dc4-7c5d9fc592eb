import Taro, { useRouter } from '@tarojs/taro'
import useObj<PERSON>tom from '@/hooks/useObjAtom'
import { activePathState, userinfoState } from '@/store/global'
import { useState, useEffect } from 'react'
import { indexPop, couponReceive } from '@/api/coupon'
import { useAsyncFn, useUpdateEffect } from 'react-use'
import { nouponListState, overDueListState } from '@/store/coupon'
import dayjs from 'dayjs'
import { Toast } from '@taroify/core'
import './index.scss'

const GetCoupon = ({ mode }: { mode: 'index' | 'my' }) => {
  const userinfo = useObjAtom(userinfoState)
  const activePath = useObjAtom(activePathState)
  const [getGiftVisible, setGiftVisible] = useState(false)
  const [notGetGiftVisible, setNotGetGiftVisible] = useState(false)
  // 优惠券
  const nouponList = useObj<PERSON>tom(nouponListState)
  const overNouponList = useObjAtom(overDueListState)

  const router = useRouter()

  const [overDue, setOverDue] = useState([])

  const [getOrderListState, getOrderListFetch] = useAsyncFn(async () => {
    const res = await indexPop()
    nouponList.set(res.data)

    getOverDueDataFn(res.data)

    // const msLeft = dayjs().endOf('day').valueOf() - dayjs().valueOf()
    // const hours = Math.floor(msLeft / (1000 * 60 * 60))
    // const minutes = Math.floor((msLeft % (1000 * 60 * 60)) / (1000 * 60))
    // const seconds = Math.floor((msLeft % (1000 * 60)) / 1000)

    // const timeLeft = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    // console.log('zzzzzzzzzz', timeLeft) // "05:23:17"

    return res
  }, [userinfo.val])

  useEffect(() => {
    if (!notGetGiftVisible) {
      // getOverDueDataFn(nouponList.val)
    }
  }, [])

  // 一键领取优惠券
  const receiveConpouFn = async () => {
    let idStr = nouponList.val.map((item: any) => item.id).toString()
    console.log('优惠券列表', nouponList, idStr)
    const res = await couponReceive(idStr)
    console.log('response  优惠券列表', res.data)
    if (res.code === 200) {
      setNotGetGiftVisible(false)
      getOrderListFetch()
      Toast.open({ type: 'success', message: '领取成功' })
    } else {
      Taro.showToast({
        title: res.msg || '领取失败',
        icon: 'none'
      })
    }
  }
  // 单个领取优惠券
  const reviceCouponItem = async (item) => {
    const res = await couponReceive(item.id)
    console.log('response  优惠券列表', res.data)
    if (res.code === 200) {
      // setNotGetGiftVisible(false)
      getOrderListFetch()
      Toast.open({ type: 'success', message: '领取成功' })
    } else {
      Taro.showToast({
        title: res.msg || '领取失败',
        icon: 'none'
      })
    }
  }
  // 获取即将过期优惠券数据
  const getOverDueDataFn = (resData) => {
    if (resData?.length === 0) return

    // 获取即将过期的优惠券
    const overDueData = resData.filter((item) => dayjs(item.endTime).isBefore(dayjs().endOf('day')))
    setOverDue(overDueData)
    overNouponList.set(overDueData)
    console.log('即将过期的优惠券', overDueData)
    if (overDueData && overDueData.length > 0) {
      setGiftVisible(true)
    }

    resData.length > 0 && overDueData?.length === 0 && setNotGetGiftVisible(true)
  }

  useUpdateEffect(() => {
    if (userinfo.val?.userId) {
      getOrderListFetch()
    }
  }, [userinfo.val])

  return (
    <>
      {mode === 'my' && nouponList.val.length > 0 && overDue?.length == 0 && (
        <div className="reveiceCoupon">
          <div className="leftIcon"></div>
          <div className="center">
            <div className="topTitle">恭喜获得200元代金券</div>
            <div className="bottomXianZhi">限时满减+无门槛代金券</div>
          </div>
          <div className="rightBtn" onClick={() => receiveConpouFn()}>
            一键领取
          </div>
        </div>
      )}

      {getGiftVisible && router.path != '/pages/my/index' && (
        <div className="shadowBg">
          <div className="dialogNoupou">
            <div className="hint">代金券即将过期</div>
            <div className="use">请及时使用</div>
            <div className="scollBox">
              {overDue?.map((item) => (
                <>
                  <div className="noupon">
                    <div className="leftItem">
                      <div className="content">
                        <div className="count">x{item.totalNum}</div>
                        <div className="moneyCount">
                          {item.reducePrice / 100}
                          <span className="unit">元</span>
                        </div>
                        <div className="xianzhi">{item.fullPrice / 100 > 0 ? `满${item.fullPrice / 100}可用` : '无门槛'}</div>
                      </div>
                    </div>
                    <div className="rightItem">
                      <div className="title">{item.couponName}</div>
                      <div className="time">
                        {dayjs(item.endTime - dayjs().valueOf()).format('HH:mm:ss')} <span className="timeText">后过期</span>
                      </div>
                      <div className="useTitle">全店可用</div>
                    </div>
                  </div>
                </>
              ))}
            </div>

            <div
              className="btn"
              onClick={() => {
                Taro.switchTab({
                  url: '/pages/inspiration/index',
                  success: () => {
                    activePath.set('/pages/inspiration/index')
                  }
                })
              }}
            >
              去使用
            </div>

            <div className="closeBtn" onClick={() => setGiftVisible(false)}>
              ×
            </div>
          </div>
        </div>
      )}

      {mode === 'index' && notGetGiftVisible ? (
        <div className="shadowBg">
          <div className="dialogNoupou-notGet">
            <div className="moneyValueIcon"></div>
            <div className="coloured"></div>
            <div className="hint">200元代金券</div>
            <div className="use">限时满减+无门槛代金券</div>
            <div className="scollBox">
              {nouponList.val.map((item: any) => (
                <div className="noupon" key={item.id}>
                  <div className="leftItem">
                    <div className="content">
                      <div className="count">x{item.totalNum}</div>
                      <div className="moneyCount">
                        {item.reducePrice / 100}
                        <span className="unit">元</span>
                      </div>
                      <div className="xianzhi">{item.fullPrice / 100 > 0 ? `满${item.fullPrice / 100}可用` : '无门槛'}</div>
                    </div>
                  </div>
                  <div className="rightItem">
                    <div className="leftContentBox">
                      <div className="title">{item.couponName}</div>
                      <div className="time">有效期至{dayjs(item.endTime).format('YYYY.MM.DD')}</div>
                      <div className="useTitle">全店可用</div>
                    </div>
                    <div className="rightBtn">
                      <div className="btn" onClick={() => reviceCouponItem(item)}>
                        领取
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div className="juxing">
            <div className="juxing_btn" onClick={() => receiveConpouFn()}>
              一键领取
            </div>
            <div className="closeBtn_dialog" onClick={() => setNotGetGiftVisible(false)}>
              ×
            </div>
          </div>
        </div>
      ) : null}
    </>
  )
}

export default GetCoupon
