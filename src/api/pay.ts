import { api1Request } from '@/utils/request'

// 响应接口
export interface QueryPayOrderRes {
  data: {
    // 订单状态（UNPAID-待支付，PAID-已支付（待发货），SHIPPED-已发货，FINISHED-已完成，CANCELLED-已取消） */
    orderStatus: 'UNPAID' | 'PAID' | 'SHIPPED' | 'FINISHED' | 'CANCELLED'
    /*订单号 */
    tradeNo: string
  }
}

/**
 * JSAPI-查询订单支付状态
 * @param {string} tradeNo 交易订单号
 * @returns
 */
export function queryPayOrder(tradeNo: string): Promise<QueryPayOrderRes> {
  return api1Request.post({
    url: `/api/pay/wx/pay/order/info?tradeNo=${tradeNo}`
  })
}

// 参数接口
export interface JsapiWxPrepayParams {
  /*交易号 */
  tradeNo?: string
  /*openId */
  openId?: string
}

// 响应接口
export interface JsapiWxPrepayRes {
  data: {
    timeStamp: number
    package: string
    tradeNo: string
    paySign: string
    nonceStr: string
  }
}

/**
 * JSAPI-微信支付参数
 * @param {object} params 小程序支付
 * @param {string} params.tradeNo 交易号
 * @param {string} params.openId openId
 * @returns
 */
export function jsapiWxPrepay(params: JsapiWxPrepayParams): Promise<JsapiWxPrepayRes> {
  return api1Request.post({
    url: `/api/pay/jsapi/wx/prepay`,
    data: params
  })
}

// 响应接口
export interface WxPayNotifyRes {
  /* */
  code: number
  /* */
  msg: string
  /* */
  data: string
}

/**
 * 微信支付回调
 * @returns
 */
export function wxPayNotify(): Promise<WxPayNotifyRes> {
  return api1Request.post({
    url: `/api/pay/jsapi/wx/payNotify`
  })
}

// 响应接口
export interface DesignTemplateDetailRes {
  data: {
    /*标题 */
    title: string
    /*封面图片地址 */
    imageUrl: string
    /*价格 */
    price: number
    /*diy的json数据 */
    diyData: string
    /*母版CODE */
    templateCode: string
    /*版型样式code */
    styleCode: string
    /*颜色 */
    colour: string
    /*男、女、儿童 */
    gender: string
    /*标签ID列表 */
    tagIds: number[]
    /*印花图片列表多个逗号分隔 */
    printingImage: string
    /*计算价格数据 */
    computePriceData: {
      /*总购买数量 */
      totalNum: number
      /*总价格 */
      totalPrice: number
      /*前面工艺ID */
      frontPrintingId: number
      /*前面工艺 */
      frontPrinting: string
      /*前面面积 (单位:平方厘米㎡) */
      frontArea: number
      /*前面价格 */
      frontPrice: number
      /*后面工艺ID */
      backPrintingId: number
      /*后面工艺 */
      backPrinting: string
      /*后面面积 (单位:平方厘米㎡) */
      backArea: number
      /*后面价格 */
      backPrice: number
    }
  }
}

/**
 * 用户设计模版详情
 * @param {string} id 成衣模版ID
 * @returns
 */
export function designTemplateDetail(id: string): Promise<DesignTemplateDetailRes> {
  return api1Request.get({
    url: `/api/index/design/template/detail?id=${id}`
  })
}

// 参数接口
export interface ExpiredListParams {
  /*总购买数量 */
  totalNum?: number

  /*总价格 */
  totalPrice?: number

  /*前面工艺ID */
  frontPrintingId?: number

  /*前面工艺 */
  frontPrinting?: string

  /*前面面积 (单位:平方厘米㎡) */
  frontArea?: number

  /*前面价格 */
  frontPrice?: number

  /*后面工艺ID */
  backPrintingId?: number

  /*后面工艺 */
  backPrinting?: string

  /*后面面积 (单位:平方厘米㎡) */
  backArea?: number

  /*后面价格 */
  backPrice?: number

  /*T恤基础价格 */
  basePrice?: number

  /*尺码CODE, 例如：S、M、L */
  sizeCode?: string

  /*优惠券减掉金额 */
  couponReducePrice?: number
}

export interface Expired {
  id: number //	id	integer(int64)
  couponName: string //	优惠券名称	string
  reducePrice: number //	减X金额	integer(int64)
  fullPrice: number //	满X金额可用	integer(int64)
  startTime: number //	开始日期	integer(int64)
  endTime: number //	结束日期	integer(int64)
  totalNum: number //	总数量	integer(int32)
  usedNum: number //	已使用数量	integer(int32)
  transferNum: number //	转赠数量	integer(int32)
  status: number //	状态 1：可用，0：不可用
}

// 响应接口
export interface ExpiredListRes {
  data: Expired[]
}

/**
 * 根据金额获取可用优惠券列表
 * @param {object} params 费用详情
 * @param {number} params.totalNum 总购买数量
 * @param {number} params.totalPrice 总价格
 * @param {number} params.frontPrintingId 前面工艺ID
 * @param {string} params.frontPrinting 前面工艺
 * @param {number} params.frontArea 前面面积 (单位:平方厘米㎡)
 * @param {number} params.frontPrice 前面价格
 * @param {number} params.backPrintingId 后面工艺ID
 * @param {string} params.backPrinting 后面工艺
 * @param {number} params.backArea 后面面积 (单位:平方厘米㎡)
 * @param {number} params.backPrice 后面价格
 * @param {number} params.basePrice T恤基础价格
 * @param {string} params.sizeCode 尺码CODE, 例如：S、M、L
 * @param {number} params.couponReducePrice 优惠券减掉金额
 * @returns
 */
export function expiredList(params: ExpiredListParams): Promise<ExpiredListRes> {
  // return request.post(`/coupon/order/list`, params);
  return api1Request.post({
    url: `/api/coupon/order/list`,
    data: params
  })
}
