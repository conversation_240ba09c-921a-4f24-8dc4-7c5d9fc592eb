import { Collect } from '@/store/my'
import { api1Request } from '@/utils/request'

// 响应接口
export interface CollectImageListRes {
  data: Collect[]
}

/**
 * 收藏图片列表
 * @param {string} lastId 分页最后一条数据的id，第一次可不传
 * @param {string} size 分页数量，默认20
 * @returns
 */
export function collectImageList({ lastId = '', size = '20' }): Promise<CollectImageListRes> {
  return api1Request.get({
    url: `/api/index/collect/image/list?lastId=${lastId}&size=${size}`
  })
}

interface ComputePrice {
  totalNum: number // 总购买数量	integer
  totalPrice: number // 总价格	integer
  frontPrintingId: number // 前面工艺ID	integer
  frontPrinting: string // 前面工艺	string
  frontArea: number // 前面面积 (单位:平方厘米㎡)	integer
  frontPrice: number // 前面价格	integer
  backPrintingId: number // 后面工艺ID	integer
  backPrinting: string // 后面工艺	string
  backArea: number // 后面面积 (单位:平方厘米㎡)	integer
  backPrice: number // 后面价格	integer
  basePrice: number // T恤基础价格	integer
  sizeCode: string // 尺码CODE, 例如：S、M、L	string
}

// 响应接口
export interface DesignTemplateListRes {
  4
  data: {
    list: {
      id: number //	id	integer
      title: string //	商品标题	string
      imageUrl: string //	图片地址	string
      price: number //	价格（单位分）	integer
      customNum: number //	定制数量	integer
      uiCode: string //	UI-code	string
      collect: number //	是否收藏，0-未收藏，1-已收藏	integer
      desc: string //	描述	string
      colours: string[] //	颜色列表	array	string
      computePrice: ComputePrice[]
    }[]
  }
}

/**
 * 我的成衣列表
 * @param {string} pageNum
 * @param {string} sizeNum
 * @returns
 */
export function designTemplateList(pageNum: number, sizeNum: number): Promise<DesignTemplateListRes> {
  // return request.post(`/index/design/template/list?pageNum=${pageNum}&sizeNum=${sizeNum}`);
  return api1Request.post({
    url: `/api/index/design/template/list?pageNum=${pageNum}&sizeNum=${sizeNum}`
  })
}

// 响应接口
export interface InfoEditRes {
  code: number
  data: number
}

/**
 * 编辑用户信息
 * @param {string} head 头像
 * @param {string} nickname 昵称
 * @returns
 */
export function infoEdit({ head, nickname }: { head: string; nickname: string }): Promise<InfoEditRes> {
  return api1Request.post({
    url: `/api/user/info/edit`,
    data: { head, nickname }
  })
}

// 响应接口
export interface InfoRes {
  data: {
    cid: string //	cid	string
    name: string //	昵称	string
    ctime: number // 创建时间	integer(int64)
    utime: number // 修改时间	integer(int64)
    head: string //	头像	string
    phone: string //	手机号	string
  }
}

/**
 * 获取用户信息
 * @returns
 */
export function getUserInfo(): Promise<InfoRes> {
  // return request.get(`/user/info`);
  return api1Request.get({
    url: `/api/user/info`
  })
}
