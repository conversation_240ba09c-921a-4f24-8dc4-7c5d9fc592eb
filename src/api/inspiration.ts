import { api1Request } from '@/utils/request'

export interface InspirationTab {
  name: string
  id: number
  subTags: InspirationTab[]
}

// 响应接口
export interface TagListRes {
  data: {
    tags: InspirationTab[]
  }
}

/**
 * 标签列表
 * @returns
 */
export function tagList(): Promise<TagListRes> {
  return api1Request.get({
    url: `/api/index/anon/tag/list`
  })
}

// 参数接口
export interface ReadyTemplateListParams {
  /*父标签ID */
  parentTagId?: number
  /*子标签ID */
  subTagId?: number
  /*页码 */
  pageNum?: number
  /*分页数量 */
  sizeNum?: number
}

// 响应接口
export interface ReadyTemplateListRes {
  data: {
    list: {
      id: number
      title: string
      imageUrl: string
      price: number
      customNum: number
      uiCode: string
      collect: number
    }[]
    total: number
  }
}

/**
 * 成衣列表
 * @param {object} params 产品标签数据传输对象
 * @param {number} params.parentTagId 父标签ID
 * @param {number} params.subTagId 子标签ID
 * @param {number} params.pageNum 页码
 * @param {number} params.sizeNum 分页数量
 * @returns
 */
export function readyTemplateList(params: ReadyTemplateListParams): Promise<ReadyTemplateListRes> {
  return api1Request.post({
    url: `/api/index/anon/ready/template/list`,
    data: params
  })
}

// 响应接口
export interface CollectImageRes {
  code: number
}

/**
 * 收藏图片
 * @param {number} templateId templateId
 * @param {string} cancel 0:收藏，1:取消收藏
 * @returns
 */
export function collectImage(templateId: number, cancel: string): Promise<CollectImageRes> {
  return api1Request.get({
    url: `/api/index/collect/image?templateId=${templateId}&cancel=${cancel}`
  })
}
