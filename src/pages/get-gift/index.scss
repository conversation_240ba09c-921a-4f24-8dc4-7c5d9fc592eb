.bg {
  width: 100%;
  height: 100vh;
  background: url('../../assets/images/getgift/bg.png') no-repeat;
  background-size: 100% 100%;

  .dialog {
    padding-left: 38px;
    padding-right: 38px;
    position: absolute;
    top: 60%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 654px;
    height: 590px;
    background: url('../../assets/images/getgift/dialog.png') no-repeat;
    background-size: 100% 100%;

    .dialogTitle {
      margin-top: 54px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 600;
      font-size: 50px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 56px;
      text-align: left;
      font-style: normal;
    }

    .ribbon {
      position: absolute;
      z-index: -1;
      top: 100px;
      left: 110px;
      width: 138px;
      height: 26px;
      background: url('../../assets/images/index/slice.png') no-repeat;
      background-size: 100% 100%;
    }

    .time {
      margin-top: 12px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 28px;
      color: #7c2303;
      line-height: 40px;
      text-align: left;
      font-style: normal;
      margin-bottom: 56px;
    }

    .drawBtn {
      width: 542px;
      height: 100px;
      margin: 64px auto;
      background: linear-gradient(220deg, #ff6a5e 0%, #e40633 100%);
      border-radius: 28px;
      text-align: center;
      line-height: 100px;
      color: #fff;
    }

    .geted {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 32px;
      color: #e40032;
      line-height: 44px;
      text-align: left;
      font-style: normal;
      margin-top: 94px;
      text-align: center;
    }
  }
}
