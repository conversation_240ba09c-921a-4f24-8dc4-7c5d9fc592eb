import use<PERSON>b<PERSON><PERSON><PERSON> from '@/hooks/useObjAtom'
import Taro from '@tarojs/taro'
import { useEffect, useState } from 'react'

import closeImg from '@/assets/images/order/close.png'
import { activeTabState } from '@/store/inspiration'

const NavBarTitle = () => {
  const [height, setHeight] = useState(0)
  const activeTab = useObjAtom(activeTabState)

  useEffect(() => {
    if (Taro.getEnv() === 'WEAPP') {
      const rect = Taro.getMenuButtonBoundingClientRect()
      const { top } = rect || {}
      setHeight(top + 42)
    } else {
      setHeight(100)
    }
  }, [])

  const ret = () => {
    Taro.navigateBack({ delta: 1 })
  }

  const share = () => {
    // Taro.showShareMenu({
    //   withShareTicket: true,
    //   menus: ['shareAppMessage', 'shareTimeline']
    // })
  }

  return (
    <>
      <div className="flex items-end w-full fixed top-0 z-50" style={{ height: `${height}px` }}>
        <div className="flex items-center h-[108px] w-full px-[32px]">
          <div className="w-[48px] flex_center h-[60px]">
            <img onClick={ret} className="w-[48px] h-[48px]" src={closeImg} alt="" />
          </div>
          <div className="flex-1 flex_center gap-[52px]"></div>
          <div className="w-[48px]"></div>
        </div>
      </div>
      {/* <div className="bg-white flex items-end w-full" style={{ height: `${height}px` }}></div> */}
    </>
  )
}

export default NavBarTitle
