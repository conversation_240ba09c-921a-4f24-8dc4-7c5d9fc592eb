import dayjs from 'dayjs'
import './ConpouItem.scss'

const ConputItem = (props: any) => {
  return (
    <>
      <div className="noUseCoupon">
        <div className="noUseCouponItem">
          <div className="leftItem">
            <div className="count">x{props.data.totalNum}</div>
            <div className="content">
              <div className="moneyCount">
                {props.data.reducePrice / 100}
                <span className="unit">元</span>
              </div>
              <div className="xianzhi">{props.data.fullPrice / 100 > 0 ? `满${props.data.fullPrice / 100}可用` : '无门槛'}</div>
            </div>
          </div>
          <div className="rightItem">
            <div className="leftContentBox">
              <div className="title">{props.data.couponName}</div>
              <div className="time">有效期至{dayjs(props.data.endTime).format('YYYY.MM.DD')}</div>
              <div className="useTitle">全店可用</div>
            </div>
            <div className="rightBtnBox">
              <div className="title">{props.state}</div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default ConputItem
