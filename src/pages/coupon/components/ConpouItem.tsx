import Taro from '@tarojs/taro'
import useObj<PERSON>tom from '@/hooks/useObjAtom'
import { activePathState } from '@/store/global'
import dayjs from 'dayjs'
import './ConpouItem.scss'

const ConputItem = (props: any) => {
  const activePath = useObjAtom(activePathState)
  return (
    <>
      <div className="coupon">
        <div className="couponItem">
          <div className="leftItem">
            <div className="count">x{props.data.totalNum}</div>
            <div className="content">
              <div className="moneyCount">
                {props.data.reducePrice / 100}
                <span className="unit">元</span>
              </div>
              <div className="xianzhi">{props.data.fullPrice / 100 > 0 ? `满${props.data.fullPrice / 100}可用` : '无门槛'}</div>
            </div>
          </div>
          <div className="rightItem">
            <div className="leftContentBox">
              <div className="title">{props.data.couponName}</div>
              <div className="time">有效期至{dayjs(props.data.endTime).format('YYYY.MM.DD')}</div>
              <div className="useTitle">全店可用</div>
            </div>
            <div className="rightBtnBox">
              {props.content || (
                <div className="btns">
                  <div
                    className="btn snedFri"
                    onClick={() =>
                      Taro.navigateTo({
                        url: `/pages/send-friend/index?couponId=${props.data.id}`
                      })
                    }
                  >
                    送好友
                  </div>
                  <div
                    className="btn toUse"
                    onClick={() => {
                      Taro.switchTab({
                        url: '/pages/inspiration/index',
                        success: () => {
                          activePath.set('/pages/inspiration/index')
                        }
                      })
                    }}
                  >
                    去使用
                  </div>
                </div>
              )}
              {/* <div className="title">待领取</div> */}
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default ConputItem
