import { Image } from '@tarojs/components'
import image_1Img from '@/assets/images/poster/image_1.png'
import image_2Img from '@/assets/images/poster/image_2.png'
import image_3Img from '@/assets/images/poster/image_3.png'
import image_4Img from '@/assets/images/poster/image_4.png'
import image_5Img from '@/assets/images/poster/image_5.png'
import image_6Img from '@/assets/images/poster/image_6.png'
import image_7Img from '@/assets/images/poster/image_7.png'

const Poster = () => {
  return (
    <div className="w-full relative flex flex-col">
      <Image className="w-[100%] h-[1029px]" mode="aspectFill" src={image_1Img} />
      <Image className="w-[100%] h-[1331px]" mode="aspectFill" src={image_2Img} />
      <Image className="w-[100%] h-[988px]" mode="aspectFill" src={image_3Img} />
      <Image className="w-[100%] h-[905px]" mode="aspectFill" src={image_4Img} />
      <Image className="w-[100%] h-[1303px]" mode="aspectFill" src={image_5Img} />
      <Image className="w-[100%] h-[1130px]" mode="aspectFill" src={image_6Img} />
      <Image className="w-[100%] h-[855px]" mode="aspectFill" src={image_7Img} />
    </div>
  )
}

export default Poster
