import { getUserInfo, infoEdit } from '@/api/my'
import useObj<PERSON>tom from '@/hooks/useObjAtom'
import useObjState from '@/hooks/useObjState'
import { userinfoState } from '@/store/global'
import { Image, Input } from '@tarojs/components'
import { useAsyncFn } from 'react-use'
import rigthImg from '@/assets/images/setting/right.png'
import Taro from '@tarojs/taro'
import { getMiniStsToken } from '@/api/global'
import { useEffect } from 'react'

const Setting = () => {
  const userinfo = useObjAtom(userinfoState)
  const headUrl = useObjState(userinfo.val?.head || '')
  const nickName = useObjState(userinfo.val?.nickName || '')
  const changeState = useObjState(false)

  useEffect(() => {
    headUrl.set(userinfo.val?.head || '')
    nickName.set(userinfo.val?.nickName || '')
  }, [userinfo.val])

  const [getUserInfoState, getUserInfoFetch] = useAsyncFn(async () => {
    const res = await getUserInfo()
    console.log('response', res)
    userinfo.set((v) => {
      if (v) {
        return {
          ...v,
          ...res.data,
          nickName: res.data.name || v.nickName
        }
      }
      return v
    })
    return res
  }, [])

  const [infoEditState, infoEditFetch] = useAsyncFn(
    async ({ head = headUrl.val || userinfo.val?.head || '', nickname = nickName.val || userinfo.val?.nickName || '' }) => {
      const res = await infoEdit({
        head,
        nickname
      })
      console.log('response', res)
      if (res.code === 200) {
        Taro.showToast({
          title: '修改成功',
          icon: 'none'
        })
        getUserInfoFetch()
      }
      return res
    },
    [userinfo.val, headUrl.val, nickName.val]
  )

  const chooseImage = (sourceType) => {
    Taro.chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType
    }).then(async (res) => {
      Taro.showLoading({
        title: '上传中...'
      })
      const filePath = res.tempFilePaths[0]
      console.log('filePath', filePath)
      if (!filePath) {
        return
      }
      const stsRes = await getMiniStsToken()
      const sts = stsRes.data
      console.log('sts', sts)

      const formData = {
        key: `ai-mp-wode-shop/${process.env.TARO_APP_ENV}/${Date.now()}.png`, // 上传文件名称
        policy: sts.policy, // 表单域
        'x-oss-signature-version': sts.x_oss_signature_version, // 指定签名的版本和算法
        'x-oss-credential': sts.x_oss_credential, // 指明派生密钥的参数集
        'x-oss-date': sts.x_oss_date, // 请求的时间
        'x-oss-signature': sts.signature, // 签名认证描述信息
        'x-oss-security-token': sts.security_token, // 安全令牌
        success_action_status: '200' // 上传成功后响应状态码
      }
      Taro.uploadFile({
        url: 'https://art-meta.oss-cn-hangzhou.aliyuncs.com',
        filePath: filePath,
        name: 'file', // 固定值为file
        formData: formData,
        withCredentials: false
      })
        .then((uploadRes) => {
          console.log('uploadRes', uploadRes)
          if (uploadRes.statusCode === 200) {
            const imageUrl = `${sts.cdn_url}/${formData.key}`
            infoEditFetch({
              head: imageUrl,
              nickname: nickName.val || userinfo.val?.nickName || ''
            })
            Taro.hideLoading()
          } else {
            console.error('Image upload failed:', uploadRes)
          }
        })
        .finally(() => {
          Taro.hideLoading()
        })
    })
  }

  return (
    <div className="bg-[#F8F8F8] h-screen w-full flex flex-col items-center pt-[16px] box-border">
      <div className="w-[702px] h-[138px] rounded-[16px] bg-white mb-[26px] flex_center px-[24px] box-border">
        <div className="min-w-[120px] text-[#333333] text-[28px]">头像</div>
        <div className="flex-1 mr-[20px] flex items-center justify-end">
          <div className="w-[88px] h-[88px] rounded-[44px] overflow-hidden">
            <Image
              mode="aspectFill"
              onClick={() => chooseImage(['album', 'camera'])}
              className="w-[88px] h-[88px] rounded-full overflow-hidden"
              src={headUrl.val}
            />
          </div>
        </div>
        <div>
          <Image mode="aspectFill" className="w-[30px] h-[30px]" src={rigthImg} />
        </div>
      </div>
      <div className="w-[702px] h-[108px] rounded-[16px] bg-white mb-[26px] flex_center px-[24px] box-border">
        <div className="min-w-[120px] text-[#333333] text-[28px]">用户名称</div>
        <div className="flex-1 mr-[20px] ml-[200px]">
          <Input
            className={`text-[#333333] text-[28px] ${changeState.val ? 'text-left' : 'text-right'}`}
            onInput={(e) => nickName.set(e.detail.value)}
            value={nickName.val}
            onFocus={() => changeState.set(true)}
            onBlur={() => {
              changeState.set(false)
              infoEditFetch({
                head: headUrl.val || userinfo.val?.head || '',
                nickname: nickName.val || userinfo.val?.nickName || ''
              })
            }}
            maxlength={20}
          />
        </div>
        <div>
          <Image className="w-[30px] h-[30px]" src={rigthImg} />
        </div>
      </div>
      <div
        onClick={() => {
          Taro.navigateTo({
            url: '/pages/address/index'
          })
        }}
        className="w-[702px] h-[108px] rounded-[16px] bg-white mb-[26px] flex_center px-[24px] box-border"
      >
        <div className="min-w-[120px] text-[#333333] text-[28px]">收货地址</div>
        <div className="flex-1 mr-[20px]"></div>
        <div>
          <Image className="w-[30px] h-[30px]" src={rigthImg} />
        </div>
      </div>
    </div>
  )
}

export default Setting
