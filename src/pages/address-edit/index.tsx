import { useEffect, useRef, useState } from 'react'
import Ta<PERSON>, { useRouter } from '@tarojs/taro'
import { useAsyncFn } from 'react-use'
import { useAtomValue } from 'jotai'
import { Form, Input, Button, Cascader, Toast, Field, Popup, Textarea } from '@taroify/core'
import {
  getProvinces,
  getCities,
  getCounties,
  getTowns,
  createAddress,
  editAddress,
  CreateAddressParams,
  EditAddressParams,
  addressDetail
} from '@/api/address'
import { userinfoState } from '@/store/global'
import { FormInstance } from '@taroify/core/form'
import useObjState from '@/hooks/useObjState'

interface CascaderValue {
  value: string
  label: string
  children?: CascaderValue[]
}

const Index = () => {
  const router = useRouter()
  const userinfo = useAtomValue(userinfoState)
  const { addressId } = router.params
  const isEdit = !!addressId
  const [open, setOpen] = useState(false)
  const ref = useRef<FormInstance>(null)
  const addrCode = useObjState<string[]>([])

  // 获取地址详情
  const [, addressDetailFetch] = useAsyncFn(async () => {
    const res = await addressDetail(Number(addressId))
    console.log('res', res)

    if (res) {
      const addressData = res.data

      ref.current?.setValues({
        contacts: addressData.contacts,
        phone: addressData.phone,
        detail: addressData.detail,
        address: [addressData.province, addressData.city, addressData.area, addressData.street].join('/')
      })

      addrCode.set([addressData.provinceCode, addressData.cityCode, addressData.areaCode, addressData.streetCode])

      // 自动构建级联选择器数据并回显
      await buildCascaderData(addressData)
    }
    return res.data
  }, [])

  useEffect(() => {
    if (addressId) {
      addressDetailFetch()
    }
  }, [addressId, addressDetailFetch])

  // 级联选择器的选项数据
  const [cascaderOptions, setCascaderOptions] = useState<CascaderValue[]>([])

  // 提交状态
  const [submitState, submitAction] = useAsyncFn(
    async (event) => {
      try {
        console.log('event.detail.value', event.detail.value, addrCode.val)
        const values = event.detail.value
        const addrStr = values.address.split('/')
        const params = {
          contacts: values.contacts,
          phone: values.phone,
          detail: values.detail,
          province: addrStr[0],
          provinceCode: addrCode.val[0],
          city: addrStr[1],
          cityCode: addrCode.val[1],
          area: addrStr[2],
          areaCode: addrCode.val[2],
          street: addrStr[3] || '',
          streetCode: addrCode.val[3] || ''
        }
        if (isEdit) {
          await editAddress({ ...params, id: Number(addressId) } as EditAddressParams)
          Toast.success('修改成功')
        } else {
          await createAddress(params as CreateAddressParams)
          Toast.success('添加成功')
        }
        setTimeout(() => {
          Taro.navigateBack()
        }, 1500)
      } catch (error: any) {
        if (error.errorInputs) {
          // 表单验证失败
          const firstError = error.errorInputs[0]
          Toast.fail(firstError.errors[0])
        } else {
          Toast.fail(isEdit ? '修改失败' : '添加失败')
        }
      }
    },
    [addrCode.val, isEdit, addressId, userinfo]
  )

  // 加载省份数据
  const loadProvinces = async (): Promise<CascaderValue[]> => {
    try {
      const res = await getProvinces()
      return res.data.map((item) => ({
        value: item.provinceCode,
        label: item.provinceName,
        children: []
      }))
    } catch (error) {
      console.error('获取省份数据失败:', error)
      return []
    }
  }

  // 加载城市数据
  const loadCities = async (provinceCode: string): Promise<CascaderValue[]> => {
    try {
      const res = await getCities(provinceCode)
      return res.data.map((item) => ({
        value: item.cityCode,
        label: item.cityName,
        children: []
      }))
    } catch (error) {
      console.error('获取城市数据失败:', error)
      return []
    }
  }

  // 加载区县数据
  const loadCounties = async (cityCode: string): Promise<CascaderValue[]> => {
    try {
      const res = await getCounties(cityCode)
      return res.data.map((item) => ({
        value: item.countyCode,
        label: item.countyName,
        children: []
      }))
    } catch (error) {
      console.error('获取区县数据失败:', error)
      return []
    }
  }

  // 加载街道数据
  const loadTowns = async (countyCode: string): Promise<CascaderValue[]> => {
    try {
      const res = await getTowns(countyCode)
      return res.data.map((item) => ({
        value: item.townCode,
        label: item.townName,
        isLeaf: true
      }))
    } catch (error) {
      console.error('获取街道数据失败:', error)
      return []
    }
  }

  // 构建级联选择器数据并回显选中值
  const buildCascaderData = async (addressData: any) => {
    try {
      // 加载省份数据
      const provinces = await loadProvinces()

      // 找到当前省份并加载城市数据
      const currentProvince = provinces.find((p) => p.value === addressData.provinceCode)
      if (currentProvince && addressData.cityCode) {
        const cities = await loadCities(addressData.provinceCode)
        currentProvince.children = cities

        // 找到当前城市并加载区县数据
        const currentCity = cities.find((c) => c.value === addressData.cityCode)
        if (currentCity && addressData.areaCode) {
          const counties = await loadCounties(addressData.cityCode)
          currentCity.children = counties

          // 找到当前区县并加载街道数据
          const currentCounty = counties.find((a) => a.value === addressData.areaCode)
          if (currentCounty && addressData.streetCode) {
            const towns = await loadTowns(addressData.areaCode)
            currentCounty.children = towns
          }
        }
      }

      setCascaderOptions(provinces)
    } catch (error) {
      console.error('构建级联数据失败:', error)
    }
  }

  const onChange = (val: string[]): Promise<any[]> => {
    if (val.length === 1) {
      return loadCities(val[0])
    } else if (val.length === 2) {
      return loadCounties(val[1])
    } else if (val.length === 3) {
      return loadTowns(val[2])
    }
    return Promise.resolve([]) // 返回空数组表示没有更多数据加载
  }

  // 初始化数据
  useEffect(() => {
    const initData = async () => {
      // 只在非编辑模式下加载省份数据，编辑模式下由 buildCascaderData 处理
      if (!isEdit) {
        const provinces = await loadProvinces()
        setCascaderOptions(provinces)
      }
    }
    initData()
  }, [isEdit])

  return (
    <div className="p-4 bg-gray-50 min-h-screen">
      <Form ref={ref} onSubmit={submitAction}>
        <Field label="联系人" name="contacts" rules={[{ required: true, message: '请输入联系人姓名' }]}>
          <Input placeholder="请输入联系人姓名" />
        </Field>
        <Field
          label="联系电话"
          name="phone"
          rules={[
            { required: true, message: '请输入联系电话' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
          ]}
        >
          <Input placeholder="请输入联系电话" />
        </Field>

        <Field label="所在地区" name="address" isLink onClick={() => setOpen(true)}>
          <Input readonly placeholder="请选择所在地区" />
        </Field>

        <Field label="详细地址" name="detail" rules={[{ required: true, message: '请输入详细地址' }]}>
          <Textarea style={{ height: '48px' }} limit={50} placeholder="请输入详细地址" />
        </Field>

        <div style={{ margin: '16px' }}>
          <Button block loading={submitState.loading} formType="submit">
            {isEdit ? '保存修改' : '保存地址'}
          </Button>
        </div>
      </Form>

      <Popup open={open} rounded placement="bottom" onClose={setOpen}>
        <Popup.Close />
        <Cascader
          options={cascaderOptions}
          loadData={onChange}
          title="请选择"
          swipeable
          value={addrCode.val}
          onSelect={addrCode.set}
          onChange={(_values_, options) => {
            setOpen(false)
            ref.current?.setValues({
              address: options.map((item) => item.children).join('/')
            })
          }}
        />
      </Popup>
    </div>
  )
}

export default Index
