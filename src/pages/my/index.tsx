import useObj<PERSON>tom from '@/hooks/useObjAtom'
import Taro, { useDidShow } from '@tarojs/taro'
import { Image } from '@tarojs/components'
import { userinfoState } from '@/store/global'
import { ActiveTabEnum, ActiveTabs, activeTabState } from '@/store/my'
import { getUserInfo } from '@/api/my'
import { useAsyncFn } from 'react-use'
import { useEffect, useState } from 'react'
import orderImg from '@/assets/images/my/order.png'
import couponsImg from '@/assets/images/my/coupons.png'
import connectImg from '@/assets/images/my/connect.png'
import settingImg from '@/assets/images/my/setting.png'
import { Sticky } from '@taroify/core'
import demoImg from '@/assets/images/inspiration/demo.png'
import useObjState from '@/hooks/useObjState'
import { nouponListState } from '@/store/coupon'
import GetCoupon from '@/components/GetCoupon'
import Product from './components/Product'
import Collect from './components/Collect'

const Index = () => {
  const activeTab = useObjAtom(activeTabState)
  const isWx = Taro.getEnv() === Taro.ENV_TYPE.WEAPP
  const userinfo = useObjAtom(userinfoState)
  const isSticky = useObjState(false)
  const [height, setHeight] = useState(0)
  const nouponList = useObjAtom(nouponListState)

  const [getUserInfoState, getUserInfoFetch] = useAsyncFn(async () => {
    const res = await getUserInfo()
    console.log('response', res)
    userinfo.set((v) => {
      if (v) {
        return {
          ...v,
          ...res.data
        }
      }
      return v
    })
    return res
  }, [])

  useEffect(() => {
    getUserInfoFetch()
  }, [])

  useEffect(() => {
    if (Taro.getEnv() === 'WEAPP') {
      const rect = Taro.getMenuButtonBoundingClientRect()
      const { top } = rect || {}
      setHeight(top + 42)
    } else {
      setHeight(100)
    }
  }, [])

  return (
    <>
      <div className="w-full h-full overflow-hidden relative">
        <div
          // h-[650px]
          className="fixed z-[1] left-0 top-0 flex flex-col justify-end px-[30px] w-full box-border bg-black"
          style={{ height: `${nouponList.val.length > 0 ? 350 : 270}px` }}
        >
          <div className="flex_center mb-[52px] w-full">
            <div className="w-[120px] h-[120px] mr-[20px]">
              <Image mode="aspectFill" className="w-[120px] h-[120px] rounded-full" src={userinfo.val?.head || demoImg} />
            </div>
            <div className="flex-1 flex flex-col justify-center">
              <div className="font-semibold text-[36px] text-white leading-[50px] text-left not-italic mb-[6px]">
                {userinfo.val?.nickName}
              </div>
              <div className="font-normal text-[24px] text-white leading-[34px] text-left not-italic opacity-50">
                账号：{userinfo.val?.phone}
              </div>
            </div>
            <div
              onClick={() => {
                Taro.navigateTo({
                  url: '/pages/setting/index'
                })
              }}
              className="w-[48px] h-[48px] anim_btn"
            >
              <img className="w-[48px] h-[48px]" src={settingImg} alt="" />
            </div>
          </div>
          <div className={`flex ${nouponList.val.length > 0 ? 'px-[50px]' : 'mb-[70px]'}`}>
            <div
              onClick={() => {
                Taro.navigateTo({
                  url: '/pages/order/index'
                })
              }}
              className="w-[178px] h-[92px] rounded-[20px] bg-[#FFFFFF25] flex_center mr-[12px] anim_btn"
            >
              <img className="mr-[8px] w-[30px] h-[30px]" src={orderImg} alt="" />
              <div className="font-medium text-[22px] text-white leading-[32px] text-left not-italic">我的订单</div>
            </div>
            <div
              onClick={() => {
                Taro.navigateTo({
                  url: '/pages/coupon/index'
                })
              }}
              className="w-[178px] h-[92px] rounded-[20px] bg-[#FFFFFF25] flex_center mr-[12px] anim_btn"
            >
              <img className="mr-[8px] w-[30px] h-[30px]" src={couponsImg} alt="" />
              <div className="font-medium text-[22px] text-white leading-[32px] text-left not-italic">优惠券</div>
            </div>
            <div
              onClick={() => {
                Taro.navigateTo({
                  url: '/pages/customer/index'
                })
              }}
              className="w-[178px] h-[92px] rounded-[20px] bg-[#FFFFFF25] flex_center mr-[12px] anim_btn"
            >
              <img className="mr-[8px] w-[30px] h-[30px]" src={connectImg} alt="" />
              <div className="font-medium text-[22px] text-white leading-[32px] text-left not-italic">联系客服</div>
            </div>
          </div>
          <GetCoupon mode="my" />
        </div>
        <div className="relative z-10 overflow-auto h-full pointer-events-none">
          <div className="h-[490px] pointer-events-none"></div>
          {isSticky.val && <div className="fixed top-0 left-0 w-full h-[108px] bg-white z-20 pointer-events-none"></div>}
          <div
            className={`bg-white rounded-[24px_24px_0_0] min-h-full pointer-events-auto ${nouponList.val.length > 0 ? 'mt-[150px]' : ''}`}
          >
            <Sticky onChange={isSticky.set} offsetTop={`${height * 0.7}rpx`}>
              <div className="flex items-center h-[108px] w-full bg-white rounded-[24px_24px_0_0]">
                <div className="flex-1 flex_center gap-[52px]">
                  {ActiveTabs.map((item) => {
                    return (
                      <div
                        key={item.tab}
                        className={`font-medium text-[32px] text-black leading-[44px] not-italic relative ${
                          activeTab.val === item.tab
                            ? "opacity-100 after:content-[''] after:absolute after:w-[24px] after:rounded-[3px] after:h-[4px] after:bg-black after:bottom-[-6px] after:left-[20px]"
                            : 'opacity-50'
                        }`}
                        onClick={() => {
                          activeTab.set(item.tab)
                        }}
                      >
                        {item.name}
                      </div>
                    )
                  })}
                </div>
              </div>
            </Sticky>
            <div className="relative flex-1 overflow-auto">
              {activeTab.val === ActiveTabEnum.product ? <Product /> : null}
              {activeTab.val === ActiveTabEnum.collect ? <Collect /> : null}
            </div>
          </div>
        </div>
        {isWx ? <div className="h-[154px]"></div> : null}
      </div>
    </>
  )
}

export default Index
