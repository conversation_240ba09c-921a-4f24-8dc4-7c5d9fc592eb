import useObjState from '@/hooks/useObjState'
import { getOrderList, orderCancel, orderConfirm, updateAddress } from '@/api/order'
import Taro, { useDidShow } from '@tarojs/taro'
import { useAsyncFn } from 'react-use'
import { useEffect } from 'react'
import { wxPay } from '@/utils'

// 0-待支付，100-已支付（待发货），200-已发货，300-已完成
const activeSubTags = [
  {
    id: 1,
    orderState: '',
    name: '全部'
  },
  {
    id: 2,
    orderState: '0',
    name: '待支付'
  },
  {
    id: 3,
    orderState: '100',
    name: '待发货'
  },
  {
    id: 4,
    orderState: '200',
    name: '待收货'
  },
  {
    id: 5,
    orderState: '300',
    name: '已完成'
  }
]
const Index = () => {
  const isWx = Taro.getEnv() === Taro.ENV_TYPE.WEAPP
  const activeProductGroupId = useObjState(1)
  const isOpen = useObjState({})
  const pageNum = useObjState(1)
  const pageSize = useObjState(100)

  const [getOrderListState, getOrderListFetch] = useAsyncFn(async () => {
    const orderState = activeSubTags.find((item) => item.id === activeProductGroupId.val)?.orderState
    const params: { orderState?: number; pageNum: number; pageSize: number } = {
      pageNum: pageNum.val,
      pageSize: pageSize.val
    }
    orderState && (params.orderState = Number(orderState))
    const res = await getOrderList(params)
    console.log('response', res)
    return res.data.list
  }, [activeProductGroupId.val, pageNum.val, pageSize.val])

  useEffect(() => {
    getOrderListFetch()
  }, [activeProductGroupId.val, pageNum.val, pageSize.val])

  const [orderCancelState, orderCancelFetch] = useAsyncFn(async (tradeNo) => {
    const res = await orderCancel(tradeNo)
    console.log('response', res)
    if (res.data === 1) {
      Taro.showToast({
        title: '订单已取消',
        icon: 'success'
      })
      // 重新获取订单列表
      getOrderListFetch()
    }
    return res.data
  }, [])

  const [orderConfirmState, orderConfirmFetch] = useAsyncFn(async (tradeNo) => {
    const res = await orderConfirm(tradeNo)
    console.log('response', res)
    if (res.data === 1) {
      Taro.showToast({
        title: '确认成功',
        icon: 'success'
      })
      // 重新获取订单列表
      getOrderListFetch()
    }
    return res.data
  }, [])

  const orderPayFetch = async (orderId) => {
    // 获取支付参数
    const payCode = await wxPay(orderId)
    if (payCode === 1) {
      // 重新获取订单列表
      getOrderListFetch()
    }
  }

  const fixCtime = (ctime) => {
    // 1753500655866 -> 2025-07-09
    const date = new Date(ctime)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  const fixState = (state) => {
    switch (state) {
      case 0:
        return '待支付'
      case 100:
        return '待发货'
      case 200:
        return '待收货'
      case 300:
        return '已完成'
      default:
        return '已取消'
    }
  }

  useDidShow(async () => {
    const selectedAddress = await new Promise((resolve) => {
      Taro.getStorage({ key: 'selectedAddress' })
        .then((res) => {
          console.log(res)
          resolve(res.data)
        })
        .catch((err) => {
          console.log(err)
          resolve(null)
        })
    })
    const changeAddressOrderId = await new Promise((resolve) => {
      Taro.getStorage({ key: 'changeAddressOrderId' })
        .then((res) => {
          console.log(res)
          resolve(res.data)
        })
        .catch((err) => {
          console.log(err)
          resolve(null)
        })
    })

    if (changeAddressOrderId && selectedAddress) {
      // 更新订单地址
      updateAddress(changeAddressOrderId as string, selectedAddress).then((res) => {
        console.log('update address response', res)
        Taro.removeStorageSync('selectedAddress')
        Taro.removeStorageSync('changeAddressOrderId')
        Taro.showToast({
          title: '地址更新成功',
          icon: 'success'
        })
        // 重新获取订单列表
        getOrderListFetch()
      })
    }
  })

  return (
    <>
      <div className="w-full h-screen bg-[#F8F8F8] overflow-hidden flex flex-col">
        {/* <NavBarTitle /> */}
        <div className="relative flex-1 overflow-auto mt-[18px]">
          <div className="flex flex-col h-full">
            <div className="h-[50px] w-full pb-[28px]">
              <div className="flex gap-[20px] h-[60px] items-center overflow-auto px-[30px] no-scrollbar">
                {activeSubTags.map((item) => (
                  <div
                    key={item.id}
                    className={`px-[20px] flex_center h-full rounded-full whitespace-nowrap flex-shrink-0 min-w-auto ${activeProductGroupId.val === item.id ? 'bg-black' : 'bg-transparent'}`}
                    onClick={() => {
                      activeProductGroupId.set(item.id)
                    }}
                  >
                    <div
                      className={`font-medium text-[26px] leading-[24px] text-left not-italic ${activeProductGroupId.val === item.id ? 'opacity-100 text-white' : 'opacity-60 text-black'}`}
                    >
                      {item.name}
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="overflow-auto flex-1">
              <div className="flex flex-wrap gap-[12px] p-[19px_19px_0]">
                {/* 订单卡片 */}
                {getOrderListState.value?.map((item) => {
                  const printing = Array.from(new Set([item.costDetail.frontPrinting, item.costDetail.backPrinting])).filter(Boolean)
                  return (
                    <div key={item.id} className="w-[700px] bg-white rounded-[16px] p-[20px] box-border mb-[20px] relative">
                      {/* 订单头部 - 日期和状态 */}
                      <div className="flex justify-between items-center h-[34px] my-[22px]">
                        <div className="font-normal text-[24px] text-black leading-[34px] text-left not-italic">{fixCtime(item.ctime)}</div>
                        <div className="font-normal text-[24px] text-black leading-[34px] text-left not-italic">
                          {fixState(item.orderState)}
                        </div>
                      </div>

                      <div className="w-[660px] h-[2px] opacity-10 bg-[#000000] mb-[30px]"></div>

                      {/* 商品信息 */}
                      <div className="h-[200px] flex_center">
                        {/* 商品图片 */}
                        <div className="w-[200px] h-[200px] rounded-[16px]">
                          <img className="w-[200px] h-[200px] rounded-[16px]" src={item.productImage.split(';')[0]} alt="商品图片" />
                        </div>

                        {/* 商品详情 */}
                        <div className="flex-1 h-full flex flex-col ml-[20px] mr-[40px] justify-between">
                          <div className="font-medium text-[32px] text-black leading-[44px] text-left not-italic line-clamp-1 mb-[10px]">
                            {item.productTitle}
                          </div>
                          <div className="font-normal text-[24px] text-black leading-[32px] text-left not-italic line-clamp-2">
                            {item.productDesc || ''}
                          </div>

                          {/* 工艺标签 */}
                          <div className="flex flex-1 items-end">
                            {printing.map((print) => (
                              <div
                                key={print}
                                className="flex_center bg-[#E406330C] mr-[12px] px-[12px] h-[40px] border rounded-[8px] border-solid border-[rgba(228,6,51,0.25)] font-normal text-[20px] text-[#E40633] leading-[20px] text-left not-italic"
                              >
                                {print}
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* 价格和数量 */}
                        <div className="flex h-full flex-col items-end justify-start">
                          <div className="h-[32px] flex items-end mt-[6px] mb-[16px]">
                            <div className="text-[20px] text-black leading-[20px] text-left not-italic">¥</div>
                            <div className="text-[32px] text-black leading-[32px] text-left not-italic">{item.totalPrice / 100}</div>
                          </div>
                          <div className="font-normal text-[24px] text-black leading-[32px] opacity-50 text-right not-italic">
                            x{item.costDetail.totalNum}
                          </div>
                        </div>
                      </div>

                      <div className="mb-[28px] mt-[20px] w-[660px] bg-[#F8F8F8] rounded-[16px] p-[16px] box-border">
                        {/* 费用详情 */}
                        <div className="h-[40px] flex justify-between items-center">
                          <div
                            className="h-full flex items-center"
                            onClick={() => {
                              isOpen.set((v) => ({ ...v, [item.id]: !v[item.id] }))
                            }}
                          >
                            <span className="font-normal text-[20px] text-black leading-[28px] text-left not-italic opacity-50">
                              费用详情
                            </span>
                            <span className="font-normal text-[20px] text-black leading-[28px] text-left not-italic opacity-50 ml-[10px]">
                              ^
                            </span>
                          </div>

                          <div className="flex items-end">
                            <span className="font-normal text-[20px] text-black leading-[28px] text-left not-italic">总计：</span>
                            <span className="text-[20px] text-black leading-[28px] text-left not-italic">¥</span>
                            <span className="text-[40px] text-black leading-[34px] text-left not-italic">{item.totalPrice / 100}</span>
                          </div>
                        </div>
                        {isOpen.val[item.id] && (
                          <>
                            <div className="w-[630px] h-[4px] opacity-10 bg-black my-[16px]"></div>
                            {/* 费用明细 */}
                            <div className="">
                              <div className="flex justify-between mb-[24px]">
                                <div className="font-normal text-[24px] text-black leading-[34px] text-left not-italic">
                                  T恤： 件数 x{item.costDetail.totalNum}
                                </div>
                                <div className="font-medium text-[28px] text-black leading-[40px] text-right not-italic">
                                  ¥{(item.costDetail.basePrice * item.costDetail.totalNum) / 100}
                                </div>
                              </div>
                              <div className="flex justify-between mb-[24px]">
                                <div className="font-normal text-[24px] text-black leading-[34px] text-left not-italic flex">
                                  正面：{item.costDetail.frontPrinting}&ensp;
                                  <span className="text-[#9f9f9f]">{item.costDetail.frontArea || 0}cm</span>
                                </div>
                                <div className="font-medium text-[28px] text-black leading-[40px] text-right not-italic">
                                  ¥{(item.costDetail.frontPrice * item.costDetail.totalNum) / 100}
                                </div>
                              </div>
                              <div className="flex justify-between mb-[24px]">
                                <div className="font-normal text-[24px] text-black leading-[34px] text-left not-italic flex">
                                  背面：{item.costDetail.backPrinting}&ensp;
                                  <span className="text-[#9f9f9f]">{item.costDetail.backArea || 0}cm</span>
                                </div>
                                <div className="font-medium text-[28px] text-black leading-[40px] text-right not-italic">
                                  ¥{(item.costDetail.backPrice * item.costDetail.totalNum) / 100}
                                </div>
                              </div>
                              {item.costDetail?.couponReducePrice ? (
                                <div className="flex justify-between mb-[24px]">
                                  <div className="font-normal text-[24px] text-black leading-[34px] text-left not-italic">优惠券:</div>
                                  <div className="font-medium text-[28px] text-[#E40633] leading-[40px] text-right not-italic">
                                    -¥{item.costDetail?.couponReducePrice / 100}
                                  </div>
                                </div>
                              ) : null}
                            </div>
                          </>
                        )}
                      </div>

                      {/* 底部按钮 */}
                      <div className="flex justify-end mb-[30px]">
                        <div className="flex-1"></div>
                        {item.orderState === 0 ? (
                          <div
                            onClick={() => {
                              Taro.showModal({
                                title: '取消订单',
                                content: '确认取消此订单吗？',
                                confirmColor: '#FF3B30',
                                success: (res) => {
                                  if (res.confirm) {
                                    orderCancelFetch(item.tradeNo)
                                  }
                                }
                              })
                            }}
                            className="mr-[20px] flex_center border-[2px] border-solid border-black bg-white box-border h-[60px] px-[22px] min-w-[140px] rounded-[16px] font-medium text-[24px] text-black leading-[24px] text-center not-italic"
                          >
                            取消订单
                          </div>
                        ) : null}
                        {item.orderState === 100 ? (
                          <div
                            onClick={() => {
                              Taro.navigateTo({
                                url: `/pages/address/index?isCheck=1`,
                                success: () => {
                                  Taro.setStorageSync('changeAddressOrderId', item.id) // 存储订单ID以便更新地址
                                }
                              })
                            }}
                            className="mr-[20px] flex_center border-[2px] border-solid border-black bg-white box-border h-[60px] px-[22px] min-w-[140px] rounded-[16px] font-medium text-[24px] text-black leading-[24px] text-center not-italic"
                          >
                            修改地址
                          </div>
                        ) : null}
                        {item.orderState === 200 ? (
                          <div
                            onClick={() => {
                              Taro.navigateTo({
                                url: `/pages/order-express/index?expressId=${item.expressId}`
                              })
                            }}
                            className="mr-[20px] flex_center border-[2px] border-solid border-black bg-white box-border h-[60px] px-[22px] min-w-[140px] rounded-[16px] font-medium text-[24px] text-black leading-[24px] text-center not-italic"
                          >
                            查看物流
                          </div>
                        ) : null}
                        {item.orderState === 200 ? (
                          <div
                            onClick={() => {
                              Taro.showModal({
                                title: '确认收货',
                                content: '确认已收到商品吗？',
                                confirmColor: '#FF3B30',
                                success: (res) => {
                                  if (res.confirm) {
                                    orderConfirmFetch(item.tradeNo)
                                  }
                                }
                              })
                            }}
                            className="mr-[20px] flex_center border-[2px] border-solid border-black bg-white box-border h-[60px] px-[22px] min-w-[140px] rounded-[16px] font-medium text-[24px] text-black leading-[24px] text-center not-italic"
                          >
                            确认收货
                          </div>
                        ) : null}
                        {item.orderState === 0 ? (
                          <div
                            onClick={() => orderPayFetch(item.tradeNo)}
                            className="flex_center h-[60px] px-[22px] min-w-[140px] rounded-[16px] bg-black font-medium text-[24px] !text-white leading-[24px] text-center not-italic"
                          >
                            去付款
                          </div>
                        ) : null}
                      </div>
                    </div>
                  )
                })}
              </div>
              {/* - END - */}
              <div className="my-[38px] w-full font-normal text-[20px] text-[#202020] leading-[44px] not-italic flex_center">
                {getOrderListState.value?.length ? '- END -' : '- No Data -'}
              </div>
            </div>
          </div>
        </div>
        {isWx ? <div className="h-[154px]"></div> : null}
      </div>
    </>
  )
}

export default Index
