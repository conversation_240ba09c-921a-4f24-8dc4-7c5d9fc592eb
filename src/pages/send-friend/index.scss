$bgColor: #efefef;
$marginTop: 16px;
$boxpadding: 24px;

.taroify-input__placeholder {
  font-size: 30px;
}

.continer {
  width: 100%;
  height: 100vh;
  padding: 24px 24px;
  box-sizing: border-box;

  .noupon {
    background-color: $bgColor;
    width: 100%;
    height: 298px;
    background: #feffff;
    border-radius: 16px;
    margin-top: $marginTop;
    padding-left: $boxpadding;
    padding-right: $boxpadding;
    box-sizing: border-box;

    .toptitle {
      height: 88px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      line-height: 88px;

      .daijinquan {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 28px;
        color: #333333;
        line-height: 40px;
        text-align: left;
        font-style: normal;
      }

      .change {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 24px;
        color: #696969;
        line-height: 34px;
        text-align: left;
        font-style: normal;
      }
    }

    .couponItem {
      width: 100%;
      height: 160px;
      display: flex;
      // background-color: #fbefdf;
      // border-radius: 8px;
      background: url('../../assets/images/coupon/bg.png') no-repeat;
      background-size: 100% 100%;

      .leftItem {
        width: 25%;
        height: 100%;
        position: relative;

        .count {
          position: absolute;
          top: 0;
          left: 0;
          color: #e40633;
          font-size: 20px;
          text-align: center;
          font-weight: 500;
          font-family:
            PingFangSC,
            PingFang SC;
          width: 64px;
          height: 48px;
          background: url('../../assets/images/coupon/countTitle.png') no-repeat;
          background-size: 100% 100%;
        }

        .content {
          margin-top: 15px;
          box-sizing: border-box;
          text-align: center;

          .moneyCount {
            margin-top: 30px;
            height: 60px;
            line-height: 60px;
            color: #e60f37;
            font-size: 45px;
            font-weight: 700;
            font-family: LiGothicMed;

            .unit {
              font-size: 28px;
              display: inline;
            }
          }

          .xianzhi {
            font-family:
              PingFangSC,
              PingFang SC;
            font-size: 20px;
            color: #e60f37;
          }
        }
      }
      .rightItem {
        width: 75%;
        padding: 10px 30px 10px;
        box-sizing: border-box;
        position: relative;
        display: flex;
        font-size: 20px;

        .leftContentBox {
          width: 50%;
          font-size: 20px;

          .title {
            padding-top: 10px;
            color: #000000;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 700;
            font-size: 24px;
            color: #000000;
            line-height: 34px;
          }

          .time {
            color: #696969;
            margin-bottom: 10px;
          }

          .useTitle {
            color: #696969;
          }
        }

        .rightBtnBox {
          width: 50%;
          position: relative;

          .btns {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            display: flex;
            justify-content: space-evenly;
            align-items: center;
            height: 56px;

            .btn {
              width: 102px;
              height: 52px;
              text-align: center;
              line-height: 56px;

              color: #fff;
              background: linear-gradient(220deg, #ff6a5e 0%, #e40633 100%);
              border-radius: 8px;
            }

            .snedFri {
              color: #e71037;
              font-family:
                PingFangSC,
                PingFang SC;
              font-weight: 500;
              font-size: 20px;
              line-height: 52px;
              text-align: center;
              font-style: normal;
              background: url('../../assets/images/coupon/btn1.png') no-repeat;
              background-size: 100% 100%;
            }

            .toUse {
              color: #fff;
              font-family:
                PingFangSC,
                PingFang SC;
              font-weight: 500;
              font-size: 20px;
              line-height: 52px;
              text-align: center;
              font-style: normal;
              background: url('../../assets/images/coupon/btn2.png') no-repeat;
              background-size: 100% 100%;
            }
          }

          .title {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #696969;
          }
        }
      }
    }
  }

  .snedCount {
    background-color: $bgColor;
    width: 100%;
    height: 112px;
    background: #feffff;
    border-radius: 16px;
    margin-top: $marginTop;

    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: $boxpadding;
    padding-right: $boxpadding;
    box-sizing: border-box;

    .snedAllCount {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 28px;
      width: 70%;
      color: #333333;
      line-height: 40px;
      text-align: left;
      font-style: normal;
    }

    .countInput {
      display: flex;
      align-items: center;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 32px;
      color: #292929;
      line-height: 44px;
      text-align: right;
      font-style: normal;
    }
  }

  .setting {
    position: relative;
    background-color: $bgColor;
    width: 100%;
    height: 318px;
    background: #feffff;
    border-radius: 16px;
    margin-top: $marginTop;
    padding-left: $boxpadding;
    padding-right: $boxpadding;
    padding-top: $boxpadding;
    box-sizing: border-box;

    .countInput_many {
      position: absolute;
      bottom: 95px;
      right: $boxpadding;
      display: flex;
      align-items: center;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 32px;
      color: #292929;
      line-height: 44px;
      text-align: right;
      font-style: normal;
    }

    .custom-color {
      --radio-checked-icon-background-color: #ee0a24;
      --radio-checked-icon-border-color: #ee0a24;
    }

    .title {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #333333;
      line-height: 40px;
      text-align: left;
      font-style: normal;
    }

    .taroify-radio {
      height: 80px;

      .taroify-radio__icon {
        width: 50px;
        height: 50px;
        font-size: 50px;

        .van-icon {
          height: 100%;
          width: 100%;
        }

        .van-icon::before {
          content: '\e728';
          width: 50px;
          height: 50px;
        }
      }

      .taroify-radio__label {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 28px;
        color: #333333;
        line-height: 40px;
        text-align: left;
        font-style: normal;
      }
    }
  }

  .advice {
    background-color: $bgColor;
    width: 100%;
    height: 112px;
    background: #feffff;
    border-radius: 16px;
    margin-top: $marginTop;

    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: $boxpadding;
    padding-right: $boxpadding;
    box-sizing: border-box;

    .adviceTitle {
      width: 60%;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 28px;
      color: #333333;
      line-height: 40px;
      text-align: left;
      font-style: normal;
    }

    .adviceInput {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 32px;
      color: #292929;
      line-height: 44px;
      text-align: right;
      font-style: normal;
    }
  }

  .beizhu {
    margin-top: 30px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 24px;
    color: #b8b8b8;
    line-height: 38px;
    text-align: left;
    font-style: normal;
  }

  .sendBtn {
    position: fixed;
    bottom: 30px;

    width: 670px;
    height: 80px;
    background: #000000;
    border-radius: 16px;
    color: #fff;
    text-align: center;
    line-height: 80px;
    font-size: 28px;
  }
}

.custom-color {
  --radio-checked-icon-background-color: #ee0a24;
  --radio-checked-icon-border-color: #ee0a24;
}

.shareTitle {
  .text {
    margin-top: 40px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 700;
    font-size: 36px;
    color: #000000;
    line-height: 50px;
    text-align: center;
    font-style: normal;
  }
}

.shareBtn::after {
  border: none;
}

.shareBtn {
  position: absolute;
  bottom: 30%;
  left: 50%;
  transform: translate(-50%, 0);
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: transparent;
  border: none;
  border-radius: none;

  .icon {
    margin: 0 auto;
    width: 100px;
    height: 100px;
    background: url('../../assets/images/sned-friend/wechat.png') no-repeat;
    background-size: 100% 100%;
  }

  .text {
    margin-top: 12px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 24px;
    color: #000000;
    line-height: 34px;
    text-align: center;
    font-style: normal;
  }
}
