import dayjs from 'dayjs'
import { useState, useEffect } from 'react'
import './selectConpou.scss'

const Index = (props) => {
  const [defaultSelect, setDefaultSelect] = useState(props.couponId)
  const [validCouponList, setValidCouponList] = useState<Array<any>>([])

  const selectCurrentConpouItem = (item) => {
    props.updateSelectConpouItem(item)
    setDefaultSelect(item.id)
  }

  useEffect(() => {
    let arr = props.nouponList.filter((item) => item.totalNum > 0)
    setValidCouponList(arr)
  }, [])

  return (
    <>
      <div className="selectDialog">
        <div className="header">
          <div className="title">选择代金券</div>
          <div className="closeBtn" onClick={() => props.updateVisible(false)}>
            ×
          </div>
        </div>
        <div className="body">
          {validCouponList.map((item) => {
            return (
              <>
                <div className="couponItem">
                  <div className="leftItem">
                    <div className="count">x{item.totalNum}</div>
                    <div className="content">
                      <div className="moneyCount">
                        {item.reducePrice / 100}
                        <span className="unit">元</span>
                      </div>
                      <div className="xianzhi">{item.fullPrice / 100 > 0 ? `满${item.fullPrice / 100}可用` : '无门槛'}</div>
                    </div>
                  </div>
                  <div className="rightItem">
                    <div className="leftContentBox">
                      <div className="title">{item.couponName}</div>
                      <div className="time">有效期至{dayjs(item.endTime).format('YYYY.MM.DD')}</div>
                      <div className="useTitle">全店可用</div>
                    </div>
                    <div className="rightBtnBox">
                      <div className="btns">
                        {defaultSelect == item.id ? (
                          <div className="active">已选择</div>
                        ) : (
                          <div className="btn" onClick={() => selectCurrentConpouItem(item)}>
                            选择
                          </div>
                        )}
                      </div>
                      {/* <div className="text">已选择</div> */}
                    </div>
                  </div>
                </div>
              </>
            )
          })}
        </div>
      </div>
    </>
  )
}

export default Index
