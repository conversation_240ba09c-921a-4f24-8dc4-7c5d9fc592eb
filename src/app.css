@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }

  .anim_btn {
    -webkit-tap-highlight-color: transparent;
    @apply active:scale-90 transition cursor-pointer;
  }

  .flex_center {
    @apply flex justify-center items-center;
  }

  @keyframes float {
    0%,
    100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-10px);
    }
  }
  .submit_btn_shadow {
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.2);
  }
  .color_shadow {
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  }
  .weui-input {
    height: 100% !important;
  }
  .hide-after::after {
    display: none;
  }

  .c-line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    /* autoprefixer: ignore next */
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
  .c-line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    /* autoprefixer: ignore next */
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .coupon-check {
    --radio-checked-icon-background-color: #ee0a24;
    --radio-checked-icon-border-color: #ee0a24;
  }
}

:root {
  --rv-field-icon-size: 28px;
}
