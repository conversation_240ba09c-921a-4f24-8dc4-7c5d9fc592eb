import Taro from '@tarojs/taro'

// H5录音管理器
export class H5RecorderManager {
  private mediaRecorder: MediaRecorder | null = null
  private audioChunks: Blob[] = []
  private stream: MediaStream | null = null
  private startTime: number = 0
  private onStopCallback: ((res: any) => void) | null = null

  async start(options: { duration: number; format: string }) {
    console.log('H5录音配置:', options)
    try {
      // 请求麦克风权限
      this.stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100
        }
      })

      // 检查浏览器支持的音频格式
      const mimeType = this.getSupportedMimeType()
      this.mediaRecorder = new MediaRecorder(this.stream, { mimeType })

      this.audioChunks = []
      this.startTime = Date.now()

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data)
        }
      }

      this.mediaRecorder.onstop = () => {
        const duration = Date.now() - this.startTime
        const audioBlob = new Blob(this.audioChunks, { type: mimeType })

        // 创建临时URL
        const tempFilePath = URL.createObjectURL(audioBlob)

        if (this.onStopCallback) {
          this.onStopCallback({
            duration,
            tempFilePath,
            fileSize: audioBlob.size,
            audioBlob // 添加blob对象，便于后续处理
          })
        }
      }

      this.mediaRecorder.onerror = (event) => {
        console.error('录音错误:', event)
        if (this.onStopCallback) {
          this.onStopCallback({
            duration: 0,
            error: '录音发生错误'
          })
        }
      }

      this.mediaRecorder.start()
    } catch (error) {
      console.error('H5录音启动失败:', error)
      let errorMessage = '录音权限未开启'

      if (error instanceof Error) {
        if (error.name === 'NotAllowedError') {
          errorMessage = '录音权限被拒绝，请允许麦克风权限'
        } else if (error.name === 'NotFoundError') {
          errorMessage = '未找到麦克风设备'
        } else if (error.name === 'NotSupportedError') {
          errorMessage = '浏览器不支持录音功能'
        }
      }

      Taro.showToast({ title: errorMessage, icon: 'none' })
    }
  }

  stop() {
    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop()
    }
    if (this.stream) {
      this.stream.getTracks().forEach((track) => track.stop())
    }
  }

  onStop(callback: (res: any) => void) {
    this.onStopCallback = callback
  }

  private getSupportedMimeType(): string {
    const types = ['audio/webm;codecs=opus', 'audio/webm', 'audio/mp4', 'audio/wav']

    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        return type
      }
    }

    return 'audio/webm' // 默认格式
  }
}
