// Qwen TTS 实时语音合成工具函数
// 使用Taro WebSocket方式实现实时语音合成

import Taro from '@tarojs/taro'

// Audio streaming setup for mini-program compatibility
let audioChunks: ArrayBuffer[] = []
let isSessionReady = false
let currentAudioContext: Taro.InnerAudioContext | null = null
let isProcessingQueue = false
let audioQueue: string[] = [] // 待播放的音频文件队列
let isPlayingQueue = false // 是否正在播放队列
let audioProcessTimer: NodeJS.Timeout | null = null // 音频处理定时器
let pendingAudioChunks: ArrayBuffer[] = [] // 待处理的音频块
let lastAudioTime = 0 // 最后一次收到音频的时间
let isTTSEnabled = true // TTS启用状态，默认启用

// Maintain a persistent Taro WebSocket instance
let persistentSocketTask: Taro.SocketTask | null = null
let socketConnected = false

// Debounce for commit events
let commitTimer: NodeJS.Timeout | null = null
const COMMIT_DEBOUNCE_DELAY = 1000 // 1秒

// 文本批量发送机制
let textBuffer: string = '' // 文本缓冲区
let sendTimer: NodeJS.Timeout | null = null
const SEND_BATCH_INTERVAL = 500 // 500毫秒批量发送间隔
let lastSendTime = 0 // 最后一次发送时间

// WebSocket 保活机制
let heartbeatTimer: NodeJS.Timeout | null = null
let reconnectTimer: NodeJS.Timeout | null = null
let reconnectAttempts = 0
const MAX_RECONNECT_ATTEMPTS = 5
const HEARTBEAT_INTERVAL = 30000 // 30秒心跳
const RECONNECT_DELAY = 3000 // 重连延迟3秒

// Immediately initialize the WebSocket connection
const endpoint = 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime'
const apiKey = process.env.TARO_APP_QIANWEN_APIKEY

// 创建小程序兼容的音频上下文
function createMiniProgramAudioContext(): Taro.InnerAudioContext {
  const audioContext = Taro.createInnerAudioContext()
  audioContext.autoplay = false
  audioContext.loop = false
  return audioContext
}

// 小程序兼容的 Base64 解码函数
function base64ToUint8Array(base64: string): Uint8Array {
  // Base64 字符集
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'

  // 移除可能的填充字符和空白字符
  const cleanBase64 = base64.replace(/[=\s]/g, '')

  // 计算输出长度
  const outputLength = Math.floor((cleanBase64.length * 3) / 4)
  const result = new Uint8Array(outputLength)

  let bufferIndex = 0
  let buffer = 0
  let bitsAccumulated = 0

  for (let i = 0; i < cleanBase64.length; i++) {
    const char = cleanBase64[i]
    const charCode = chars.indexOf(char)

    if (charCode === -1) {
      console.warn('Invalid base64 character:', char)
      continue
    }

    buffer = (buffer << 6) | charCode
    bitsAccumulated += 6

    if (bitsAccumulated >= 8) {
      bitsAccumulated -= 8
      if (bufferIndex < outputLength) {
        result[bufferIndex++] = (buffer >> bitsAccumulated) & 0xff
      }
    }
  }

  return result
}

// Debounced commit function
function debouncedCommit() {
  // Clear existing timer
  if (commitTimer) {
    clearTimeout(commitTimer)
  }

  // Set new timer
  commitTimer = setTimeout(() => {
    if (persistentSocketTask && socketConnected) {
      const commitEvent = {
        type: 'input_text_buffer.commit',
        event_id: `event_${Date.now()}`
      }
      // console.log(`发送防抖commit事件: event_id=${commitEvent.event_id}`)
      persistentSocketTask.send({
        data: JSON.stringify(commitEvent)
      })
    }
    commitTimer = null
  }, COMMIT_DEBOUNCE_DELAY)
}

// 批量发送文本函数 - 改为定时批量发送而非防抖
function batchSendText() {
  const currentTime = Date.now()

  // 如果距离上次发送时间不足500ms，设置定时器等待
  const timeSinceLastSend = currentTime - lastSendTime
  if (timeSinceLastSend < SEND_BATCH_INTERVAL) {
    // 清除现有定时器
    if (sendTimer) {
      clearTimeout(sendTimer)
    }

    // 设置新定时器，在剩余时间后发送
    const remainingTime = SEND_BATCH_INTERVAL - timeSinceLastSend
    sendTimer = setTimeout(() => {
      sendTextBuffer()
      sendTimer = null
    }, remainingTime)
    return
  }

  // 如果已经超过500ms，立即发送
  sendTextBuffer()
}

// 发送文本缓冲区内容
function sendTextBuffer() {
  if (!textBuffer.trim() || !persistentSocketTask || !socketConnected) {
    return
  }

  const eventId = `event_${Date.now()}`
  const textToSend = textBuffer.trim()

  // 发送前先清空缓冲区，避免重复发送
  textBuffer = ''
  lastSendTime = Date.now()

  // 发送累积的文本
  const appendEvent = {
    type: 'input_text_buffer.append',
    text: textToSend,
    event_id: eventId
  }

  console.log(`批量发送事件: type=${appendEvent.type}, event_id=${appendEvent.event_id}, text_length=${textToSend.length}`)

  try {
    persistentSocketTask.send({
      data: JSON.stringify(appendEvent)
    })

    // 触发commit
    debouncedCommit()
  } catch (error) {
    console.error('批量发送文本失败:', error)
    // 发送失败时将文本重新加回缓冲区
    textBuffer = textToSend + textBuffer
    // 发送失败可能是连接问题，标记为断开状态
    socketConnected = false
    handleSocketReconnect()
  }
}

// 连接检测函数 - 不发送心跳，只检测连接状态
function startConnectionCheck() {
  if (heartbeatTimer) {
    clearInterval(heartbeatTimer)
  }

  heartbeatTimer = setInterval(() => {
    // 只检测连接状态，不发送ping消息
    if (!persistentSocketTask || !socketConnected) {
      // console.log('检测到连接断开，准备重连')
      handleSocketReconnect()
    }
  }, HEARTBEAT_INTERVAL)
}

// 停止连接检测
function stopConnectionCheck() {
  if (heartbeatTimer) {
    clearInterval(heartbeatTimer)
    heartbeatTimer = null
  }
}

// 重连处理函数
function handleSocketReconnect() {
  if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
    console.error('WebSocket重连次数超过最大限制，停止重连')
    return
  }

  // console.log(`准备重连WebSocket，第${reconnectAttempts + 1}次尝试`)

  // 清理当前连接
  cleanupSocket()

  reconnectAttempts++

  reconnectTimer = setTimeout(() => {
    initializeWebSocket()
  }, RECONNECT_DELAY)
}

// 清理Socket连接
function cleanupSocket() {
  socketConnected = false
  isSessionReady = false

  // 停止连接检测
  stopConnectionCheck()

  // 清理防抖定时器
  if (commitTimer) {
    clearTimeout(commitTimer)
    commitTimer = null
  }

  // 清理批量发送定时器
  if (sendTimer) {
    clearTimeout(sendTimer)
    sendTimer = null
  }

  // 清空文本缓冲区
  textBuffer = ''

  // 清理重连定时器
  if (reconnectTimer) {
    clearTimeout(reconnectTimer)
    reconnectTimer = null
  }

  // 重置音频播放状态
  if (currentAudioContext) {
    currentAudioContext.stop()
    try {
      currentAudioContext.destroy()
    } catch (e) {}
    currentAudioContext = null
  }

  // 清理音频队列和状态
  audioChunks = []
  audioQueue = []
  pendingAudioChunks = []
  isProcessingQueue = false
  isPlayingQueue = false
  lastAudioTime = 0

  // 清理音频处理定时器
  if (audioProcessTimer) {
    clearTimeout(audioProcessTimer)
    audioProcessTimer = null
  }

  // 清理所有临时音频文件
  audioQueue.forEach((filePath) => {
    Taro.getFileSystemManager().unlink({
      filePath: filePath,
      // success: () => console.log('清理队列中的临时文件:', filePath),
      fail: (error) => console.warn('清理队列文件失败:', error)
    })
  })

  // 关闭WebSocket连接
  if (persistentSocketTask) {
    try {
      persistentSocketTask.close({})
    } catch (error) {
      console.error('关闭WebSocket时出错:', error)
    }
    persistentSocketTask = null
  }
}

// 初始化WebSocket连接
function initializeWebSocket() {
  if (!apiKey) {
    console.error('Qwen TTS API Key 未配置，无法初始化 WebSocket')
    return
  }

  // console.log('正在初始化WebSocket连接...')

  // 使用Taro WebSocket API创建连接
  Taro.connectSocket({
    url: `${endpoint}?model=qwen-tts-realtime&api_key=${apiKey}`
  })
    .then((socketTask) => {
      // console.log('socketTask', socketTask)
      persistentSocketTask = socketTask

      socketTask.onOpen(() => {
        // console.log(`Connected to server: ${endpoint}`)
        socketConnected = true
        reconnectAttempts = 0 // 重连成功，重置计数器

        // 开始连接检测
        startConnectionCheck()

        // 设置会话配置，参考Python代码的session.update
        const sessionConfig = {
          type: 'session.update',
          event_id: `event_${Date.now()}`,
          session: {
            mode: 'server_commit',
            voice: 'Cherry',
            response_format: 'pcm',
            sample_rate: 24000
          }
        }

        // console.log('更新会话配置:', sessionConfig)
        socketTask.send({
          data: JSON.stringify(sessionConfig)
        })
      })

      // 修改 WebSocket 的 onMessage 处理逻辑，参考Python代码
      socketTask.onMessage((event) => {
        const data = JSON.parse(event.data as string)
        const eventType = data.type

        if (eventType !== 'response.audio.delta') {
          // console.log(`收到事件: ${eventType}`)
        }

        switch (eventType) {
          case 'error':
            console.error('错误:', data.error || {})
            break
          case 'session.created':
            // console.log('会话创建，ID:', data.session?.id)
            break
          case 'session.updated':
            // console.log('会话更新，ID:', data.session?.id)
            isSessionReady = true
            break
          case 'input_text_buffer.committed':
            // console.log('文本缓冲区已提交，项目ID:', data.item_id)
            break
          case 'response.created':
            // console.log('响应已创建，ID:', data.response?.id)
            break
          case 'response.output_item.added':
            // console.log('输出项已添加，ID:', data.item?.id)
            break
          case 'response.audio.delta':
            // console.log('音频增量数据接收，长度:', data.delta?.length || 0)
            // 处理音频增量数据
            const audioBase64 = data.delta
            if (audioBase64) {
              playAudioPCMStream(audioBase64)
            }
            break
          case 'response.audio.done':
            // console.log('音频生成完成')
            break
          case 'response.done':
            // console.log('响应完成')
            break
          case 'session.finished':
            // console.log('会话已结束')
            break
        }
      })

      socketTask.onError((error) => {
        console.error('WebSocket Error:', error)
        socketConnected = false
        // 发生错误时尝试重连
        handleSocketReconnect()
      })

      socketTask.onClose(() => {
        // console.log('WebSocket 连接已关闭')
        socketConnected = false

        // 清理资源
        cleanupSocket()

        // 如果不是主动关闭且重连次数未超限，尝试重连
        if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
          // console.log('WebSocket意外关闭，准备重连...')
          handleSocketReconnect()
        }
      })
    })
    .catch((error) => {
      console.error('WebSocket 连接失败:', error)
      socketConnected = false
      // 连接失败时尝试重连
      handleSocketReconnect()
    })
}

// 初始化WebSocket连接
initializeWebSocket()

// Ensure proper message formatting and validation
export async function synthesizeQwenTTSRealtime(text: string): Promise<void> {
  if (!text || typeof text !== 'string') {
    console.error('Invalid text input for TTS synthesis')
    return
  }

  if (!persistentSocketTask || !socketConnected) {
    console.error('WebSocket 未连接，无法发送消息')
    // 如果连接断开，尝试重连
    if (!socketConnected) {
      handleSocketReconnect()
    }
    return
  }

  // 等待会话准备就绪
  if (!isSessionReady) {
    // console.log('等待会话准备就绪...')
    // 等待最多3秒
    let waitTime = 0
    const maxWaitTime = 3000
    const checkInterval = 100

    while (!isSessionReady && waitTime < maxWaitTime) {
      await new Promise((resolve) => setTimeout(resolve, checkInterval))
      waitTime += checkInterval

      // 如果在等待过程中连接断开，直接返回
      if (!socketConnected) {
        console.error('等待期间WebSocket连接断开')
        return
      }
    }

    if (!isSessionReady) {
      console.error('会话未能在规定时间内准备就绪')
      return
    }
  }

  // 将文本添加到缓冲区，而不是立即发送
  textBuffer += text

  // console.log(`文本已添加到缓冲区: "${text}", 当前缓冲区长度: ${textBuffer.length}`)

  // 触发批量发送
  batchSendText()
}

// 播放音频 - 小程序兼容版本
export function playAudioUrl(url: string) {
  if (!url) return

  try {
    const audioContext = createMiniProgramAudioContext()
    audioContext.src = url
    audioContext.play()

    audioContext.onError((error) => {
      console.error('音频播放出错:', error)
    })
  } catch (error) {
    console.error('创建音频播放器失败:', error)
  }
}

// PCM格式音频流处理 - 转换为小程序可播放的格式
function playAudioPCMStream(audioBase64: string) {
  try {
    // 检查TTS是否启用，如果未启用则直接返回，不处理音频
    if (!isTTSEnabled) {
      // console.log('TTS已停用，跳过音频处理')
      return
    }

    // 使用小程序兼容的 Base64 解码
    const audioBytes = base64ToUint8Array(audioBase64)

    // 收集音频数据到待处理队列
    pendingAudioChunks.push(audioBytes.buffer)
    lastAudioTime = Date.now()

    // 如果当前没有音频在播放，立即开始处理第一批音频
    if (!isPlayingQueue && pendingAudioChunks.length >= 1) {
      // console.log('立即处理第一批音频数据，确保快速开始播放')
      processAudioImmediately()
    } else {
      // 如果已经在播放，使用智能批处理策略
      scheduleAudioProcessing()
    }
  } catch (error) {
    console.error('Error processing PCM audio stream:', error)
  }
}

// 立即处理音频（用于首次播放）
function processAudioImmediately() {
  if (!isTTSEnabled || pendingAudioChunks.length === 0 || isProcessingQueue) return

  // 将待处理的音频块移到主处理队列
  audioChunks.push(...pendingAudioChunks)
  pendingAudioChunks = []

  // 立即处理
  processAudioQueue()
}

// 智能调度音频处理
function scheduleAudioProcessing() {
  // 清除之前的定时器
  if (audioProcessTimer) {
    clearTimeout(audioProcessTimer)
  }

  // 根据当前状态决定处理延迟
  let delay = 200 // 默认200ms

  // 如果没有音频在播放，快速处理
  if (!isPlayingQueue) {
    delay = 100
  }
  // 如果已经有较多音频块，延长等待时间以获得更长的音频
  else if (pendingAudioChunks.length >= 3) {
    delay = 300
  }

  audioProcessTimer = setTimeout(() => {
    // 检查是否还有新的音频在持续到达
    const timeSinceLastAudio = Date.now() - lastAudioTime

    // 如果最近200ms内没有新音频，或者已经累积了足够的音频块，就处理
    if (timeSinceLastAudio >= 200 || pendingAudioChunks.length >= 5) {
      if (pendingAudioChunks.length > 0) {
        // 将待处理的音频块移到主处理队列
        audioChunks.push(...pendingAudioChunks)
        pendingAudioChunks = []

        // 处理音频队列
        if (!isProcessingQueue) {
          processAudioQueue()
        }
      }
    } else {
      // 如果还有音频在持续到达，继续等待
      scheduleAudioProcessing()
    }

    audioProcessTimer = null
  }, delay)
}

// 处理音频队列 - 将PCM数据转换为小程序可播放的音频文件
async function processAudioQueue() {
  if (!isTTSEnabled || audioChunks.length === 0 || isProcessingQueue) return

  try {
    isProcessingQueue = true
    // console.log(`开始处理音频队列，当前音频块数量: ${audioChunks.length}`)

    // 合并所有音频块
    const totalBytes = audioChunks.reduce((total, chunk) => total + chunk.byteLength, 0)
    const mergedBuffer = new ArrayBuffer(totalBytes)
    const mergedView = new Uint8Array(mergedBuffer)

    let offset = 0
    for (const chunk of audioChunks) {
      const chunkView = new Uint8Array(chunk)
      mergedView.set(chunkView, offset)
      offset += chunk.byteLength
    }

    // console.log(`音频数据合并完成，总大小: ${totalBytes} bytes`)

    // 创建WAV格式的音频数据
    const wavBuffer = createWavBuffer(mergedView, 24000, 1, 16)

    // 在小程序中，我们需要将音频数据保存为临时文件
    const tempFilePath = await saveAudioToTempFile(wavBuffer)

    if (tempFilePath) {
      // console.log(`音频文件保存成功: ${tempFilePath}`)
      // 将音频文件加入播放队列
      audioQueue.push(tempFilePath)

      // 如果当前没有在播放队列，立即开始播放
      if (!isPlayingQueue) {
        // console.log('开始播放音频队列')
        playNextInQueue()
      }
    }

    // 清空已处理的音频块
    audioChunks = []
  } catch (error) {
    console.error('Error processing audio queue:', error)
  } finally {
    isProcessingQueue = false
  }
}

// 播放队列中的下一个音频文件
async function playNextInQueue() {
  if (!isTTSEnabled || audioQueue.length === 0) {
    isPlayingQueue = false
    return
  }

  isPlayingQueue = true
  const filePath = audioQueue.shift()!

  try {
    await playTempAudioFile(filePath)
  } catch (error) {
    console.error('播放音频文件失败:', error)
  }

  // 播放完成后，立即播放下一个（减少延迟）
  if (audioQueue.length > 0) {
    // 使用setImmediate或最小延迟来确保连贯性
    setTimeout(() => playNextInQueue(), 10) // 减少到10ms延迟
  } else {
    isPlayingQueue = false

    // 检查是否有新的音频数据等待处理
    if (pendingAudioChunks.length > 0 && !isProcessingQueue) {
      // console.log('播放队列空了，但还有待处理音频，立即处理')
      processAudioImmediately()
    }
  }
}

// 创建WAV格式音频缓冲区
function createWavBuffer(pcmData: Uint8Array, sampleRate: number, channels: number, bitsPerSample: number): ArrayBuffer {
  const dataLength = pcmData.length
  const buffer = new ArrayBuffer(44 + dataLength)
  const view = new DataView(buffer)

  // WAV文件头
  const writeString = (offset: number, string: string) => {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i))
    }
  }

  writeString(0, 'RIFF')
  view.setUint32(4, 36 + dataLength, true)
  writeString(8, 'WAVE')
  writeString(12, 'fmt ')
  view.setUint32(16, 16, true)
  view.setUint16(20, 1, true)
  view.setUint16(22, channels, true)
  view.setUint32(24, sampleRate, true)
  view.setUint32(28, (sampleRate * channels * bitsPerSample) / 8, true)
  view.setUint16(32, (channels * bitsPerSample) / 8, true)
  view.setUint16(34, bitsPerSample, true)
  writeString(36, 'data')
  view.setUint32(40, dataLength, true)

  // 复制PCM数据
  const audioData = new Uint8Array(buffer, 44)
  audioData.set(pcmData)

  return buffer
}

// 保存音频到临时文件
async function saveAudioToTempFile(audioBuffer: ArrayBuffer): Promise<string | null> {
  try {
    const fs = Taro.getFileSystemManager()
    const tempFilePath = `${Taro.env.USER_DATA_PATH}/temp_audio_${Date.now()}.wav`

    await new Promise<void>((resolve, reject) => {
      fs.writeFile({
        filePath: tempFilePath,
        data: audioBuffer,
        success: () => resolve(),
        fail: (error) => reject(error)
      })
    })

    return tempFilePath
  } catch (error) {
    console.error('保存音频文件失败:', error)
    return null
  }
}

// 播放临时音频文件
async function playTempAudioFile(filePath: string): Promise<void> {
  return new Promise((resolve, reject) => {
    // 停止当前播放的音频
    if (currentAudioContext) {
      currentAudioContext.stop()
      try {
        currentAudioContext.destroy()
      } catch (e) {}
      currentAudioContext = null
    }

    // 创建新的音频上下文
    currentAudioContext = createMiniProgramAudioContext()
    currentAudioContext.src = filePath

    let hasResolved = false // 防止重复resolve

    currentAudioContext.onPlay(() => {
      // console.log('音频开始播放:', filePath)
    })

    currentAudioContext.onEnded(() => {
      // console.log('音频播放结束:', filePath)

      if (currentAudioContext) {
        try {
          currentAudioContext.destroy()
        } catch (e) {}
        currentAudioContext = null
      }

      // 清理临时文件
      Taro.getFileSystemManager().unlink({
        filePath: filePath,
        // success: () => console.log('临时音频文件已清理:', filePath),
        fail: (error) => console.warn('清理临时文件失败:', error)
      })

      if (!hasResolved) {
        hasResolved = true
        resolve()
      }
    })

    currentAudioContext.onError((error) => {
      console.error('音频播放出错:', error)

      if (currentAudioContext) {
        try {
          currentAudioContext.destroy()
        } catch (e) {}
        currentAudioContext = null
      }

      // 即使出错也要清理文件
      Taro.getFileSystemManager().unlink({
        filePath: filePath,
        // success: () => console.log('临时音频文件已清理(错误后):', filePath),
        fail: (cleanError) => console.warn('清理临时文件失败(错误后):', cleanError)
      })

      if (!hasResolved) {
        hasResolved = true
        reject(error)
      }
    })

    currentAudioContext.onStop(() => {
      // console.log('音频播放被停止:', filePath)

      if (currentAudioContext) {
        try {
          currentAudioContext.destroy()
        } catch (e) {}
        currentAudioContext = null
      }

      if (!hasResolved) {
        hasResolved = true
        resolve() // 被停止也算完成
      }
    })

    // 设置播放超时，防止卡死
    const playTimeout = setTimeout(() => {
      if (!hasResolved) {
        console.warn('音频播放超时:', filePath)
        if (currentAudioContext) {
          currentAudioContext.stop()
        }
      }
    }, 30000) // 30秒超时

    currentAudioContext.onEnded(() => {
      clearTimeout(playTimeout)
    })

    currentAudioContext.onError(() => {
      clearTimeout(playTimeout)
    })

    currentAudioContext.play()
  })
}

// 手动重连WebSocket
export function reconnectQwenTTSWebSocket(): void {
  // console.log('手动触发WebSocket重连')
  reconnectAttempts = 0 // 重置重连计数
  handleSocketReconnect()
}

// 立即发送缓冲区中的文本（跳过批量发送延迟）
export function flushQwenTTSBuffer(): void {
  if (sendTimer) {
    clearTimeout(sendTimer)
    sendTimer = null
  }

  // 直接调用发送函数，立即发送缓冲区内容
  sendTextBuffer()
}

// 获取当前缓冲区状态
export function getQwenTTSBufferStatus(): { bufferLength: number; bufferContent: string; hasTimer: boolean } {
  return {
    bufferLength: textBuffer.length,
    bufferContent: textBuffer,
    hasTimer: sendTimer !== null
  }
}

// 获取音频播放状态
export function getQwenTTSAudioStatus(): {
  audioChunksCount: number
  pendingChunksCount: number
  audioQueueLength: number
  isPlaying: boolean
  isProcessing: boolean
  lastAudioTime: number
} {
  return {
    audioChunksCount: audioChunks.length,
    pendingChunksCount: pendingAudioChunks.length,
    audioQueueLength: audioQueue.length,
    isPlaying: isPlayingQueue,
    isProcessing: isProcessingQueue,
    lastAudioTime: lastAudioTime
  }
}

// 获取WebSocket连接状态
export function getQwenTTSConnectionStatus(): { connected: boolean; sessionReady: boolean } {
  return {
    connected: socketConnected,
    sessionReady: isSessionReady
  }
}

// 停止播放并清空音频数据
export function stopQwenTTSPlayback(): void {
  console.log('停止TTS播放并清空音频数据')

  // 设置TTS为停用状态，阻止新的音频处理
  isTTSEnabled = false

  // 停止当前播放的音频
  if (currentAudioContext) {
    currentAudioContext.stop()
    try {
      currentAudioContext.destroy()
    } catch (e) {}
    currentAudioContext = null
  }

  // 清空所有音频相关的队列和缓冲区
  audioChunks = []
  pendingAudioChunks = []
  audioQueue.forEach((filePath) => {
    // 清理队列中的临时音频文件
    Taro.getFileSystemManager().unlink({
      filePath: filePath,
      success: () => console.log('清理临时音频文件:', filePath),
      fail: (error) => console.warn('清理临时文件失败:', error)
    })
  })
  audioQueue = []

  // 重置播放状态
  isProcessingQueue = false
  isPlayingQueue = false
  lastAudioTime = 0

  // 清理音频处理定时器
  if (audioProcessTimer) {
    clearTimeout(audioProcessTimer)
    audioProcessTimer = null
  }

  // 清空文本缓冲区，停止新的音频生成
  textBuffer = ''

  // 清理文本发送定时器
  if (sendTimer) {
    clearTimeout(sendTimer)
    sendTimer = null
  }

  // 清理commit定时器
  if (commitTimer) {
    clearTimeout(commitTimer)
    commitTimer = null
  }

  console.log('TTS播放已停止，所有音频数据已清空')
}

// 启用TTS播放
export function enableQwenTTSPlayback(): void {
  console.log('启用TTS播放')
  isTTSEnabled = true
}

// 获取TTS启用状态
export function getQwenTTSEnabledStatus(): boolean {
  return isTTSEnabled
}

// 清理资源并关闭连接
export function closeQwenTTSConnection(): void {
  // console.log('主动关闭WebSocket连接')
  reconnectAttempts = MAX_RECONNECT_ATTEMPTS // 防止自动重连
  cleanupSocket()
}
