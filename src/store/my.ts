import { ReadyTemplateListRes } from '@/api/inspiration'
import { atom } from 'jotai'

export type ActiveTab = 'product' | 'pattern' | 'collect'
export const ActiveTabEnum = {
  product: 'product',
  pattern: 'pattern',
  collect: 'collect'
}
export const ActiveTabs: { name: string; tab: ActiveTab }[] = [
  {
    name: '成衣',
    tab: 'product'
  },
  {
    name: '收藏',
    tab: 'collect'
  }
]
export const activeTabState = atom<ActiveTab>('product')

export interface Collect {
  id: number
  imageUrl: string
  description: string
  customNum: number
}
export const collectListState = atom<Collect[]>([])

export const myProductListState = atom<ReadyTemplateListRes['data']['list']>([])
