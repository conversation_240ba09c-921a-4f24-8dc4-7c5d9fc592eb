/* 自定义TabBar样式 */
.custom-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background-color: #ffffff;
  border-top: 1px solid #f3f4f6;
  z-index: 999;
}

.tab-bar-container {
  display: flex;
  align-items: center;
  justify-content: space-around;
  height: 136px;
  background-color: #ffffff;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px 32px; /* 调整内边距 */
  flex: 1;
  min-width: 0;
  background: transparent;
  border: none;
  margin: 0;
  padding: 0;
  background: none;
  border: none;
  line-height: inherit;
  border-radius: 0;
}

.tab-item::after {
  display: none;
}

.tab-icon-wrapper {
  margin-bottom: 8px; /* 调整间距 */
}

.tab-icon {
  width: 56px; /* 使用 px 单位，适合小程序 */
  height: 56px;
}

.tab-icon.active {
  color: #7c3aed;
}

.tab-icon.inactive {
  color: #6b7280;
}

.tab-title {
  font-size: 24px; /* 使用 px 单位，适合小程序 */
  text-align: center;
}

.tab-title.active {
  font-weight: 600;
  color: #7c3aed;
}

.tab-title.inactive {
  font-weight: 500;
  color: #6b7280;
}

.safe-area {
  height: 16px; /* 调整安全区域高度 */
  background-color: #ffffff;
}
