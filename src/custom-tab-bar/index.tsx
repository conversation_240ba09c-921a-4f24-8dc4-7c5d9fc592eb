import useObj<PERSON>tom from '@/hooks/useObjAtom'
import { activePathState, userinfoState } from '@/store/global'
import inspirationImg from '@/assets/images/tabbar/inspiration-normal.png'
import inspirationActiveImg from '@/assets/images/tabbar/inspiration-active.png'
import myImg from '@/assets/images/tabbar/my-normal.png'
import myActiveImg from '@/assets/images/tabbar/my-active.png'
import indexImg from '@/assets/images/tabbar/ai-design-normal.png'
import indexActiveImg from '@/assets/images/tabbar/ai-design-active.png'
import Taro from '@tarojs/taro'
import { Button, Image } from '@tarojs/components'
import { phoneAuth } from '@/utils'
import './index.scss'

interface TabBarItem {
  title: string
  icon: string
  activeIcon: string
  path: string
}

// TabBar配置项
const tabItems: TabBarItem[] = [
  {
    title: 'AI设计',
    icon: indexImg,
    activeIcon: indexActiveImg,
    path: '/pages/index/index'
  },
  {
    title: '灵感',
    icon: inspirationImg,
    activeIcon: inspirationActiveImg,
    path: '/pages/inspiration/index'
  },
  {
    title: '我的',
    icon: myImg,
    activeIcon: myActiveImg,
    path: '/pages/my/index'
  }
]

const CustomTabBar = () => {
  const activePath = useObjAtom(activePathState)
  const userinfo = useObjAtom(userinfoState)

  // 授权手机号
  const getPhoneNumber = async (val) => {
    const { detail } = val
    if (detail.code) {
      const res = await phoneAuth(detail)
      if (res.code === 200) {
        userinfo.set((v) => {
          if (v) {
            return {
              ...v,
              phone: res.data
            }
          }
          return null
        })
      } else {
        Taro.showToast({
          title: `授权失败: ${res.msg}`,
          icon: 'none',
          duration: 2000
        })
      }
    } else {
      console.log('取消授权')
    }
  }

  return (
    <div className="custom-tab-bar" id="customTabBar">
      <div className="tab-bar-container">
        {tabItems.map((item, index) => {
          const isActive = activePath.val === item.path
          return userinfo.val?.phone || item.path === '/pages/index/index' ? (
            <Button
              key={index}
              className="tab-item"
              onClick={() => {
                Taro.switchTab({
                  url: item.path,
                  success: () => {
                    activePath.set(item.path)
                  }
                })
              }}
            >
              <div className="tab-icon-wrapper">
                <Image src={isActive ? item.activeIcon : item.icon} className="tab-icon" />
              </div>
              <span className={`tab-title ${isActive ? 'active' : 'inactive'}`}>{item.title}</span>
            </Button>
          ) : (
            <Button openType="getPhoneNumber" onGetPhoneNumber={getPhoneNumber} key={index} className="tab-item">
              <div className="tab-icon-wrapper">
                <Image src={isActive ? item.activeIcon : item.icon} className="tab-icon" />
              </div>
              <span className={`tab-title ${isActive ? 'active' : 'inactive'}`}>{item.title}</span>
            </Button>
          )
        })}
      </div>

      {/* 底部安全区域 */}
      <div className="safe-area" />
    </div>
  )
}

CustomTabBar.options = {
  addGlobalClass: true
}

export default CustomTabBar
